<template>
	<view class="contact-page">
		<!-- 自定义导航栏 -->
		<custom-navbar 
			title="联系我们" 
			title-color="#000000"
			icon-color="#000000"
			background-color="transparent">
		</custom-navbar>
		
		<!-- 背景图片 -->
		<image 
			class="background-image" 
			src="/static/images/drawable-hdpi_bg_home.png" 
			mode="aspectFill">
		</image>
		
		<!-- 页面内容 -->
		<view class="content-container">
			<!-- 标题区域 -->
			<view class="title-section">
				<text class="main-title">联系我们</text>
				<text class="sub-title">如有异常可及时联系我们</text>
			</view>
			
			<!-- 二维码区域 -->
			<view class="qr-section">
				<view class="qr-code-container">
					<!-- 暂时用白色背景代替二维码 -->
					<view class="qr-code-placeholder"></view>
				</view>
				<view class="qr-description">
					<text class="qr-text">扫描二维码添加客服</text>
				</view>
			</view>
			
			<!-- 联系方式区域 -->
			<view class="contact-info-section">
				<!-- 电话 -->
				<view class="contact-item">
					<view class="contact-label">
						<text class="label-text">电话</text>
					</view>
					<view class="contact-value">
						<text class="value-text">************</text>
					</view>
				</view>
				
				<!-- 手机号 -->
				<view class="contact-item">
					<view class="contact-label">
						<text class="label-text">手机号</text>
					</view>
					<view class="contact-value">
						<text class="value-text">15366668888</text>
						<view class="phone-icon-wrapper" @click="makePhoneCall">
							<uni-icons type="phone" size="20" color="#333333"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar/custom-navbar.vue'

export default {
	name: 'ContactPage',
	components: {
		CustomNavbar
	},
	data() {
		return {
			
		}
	},
	onLoad() {
		
	},
	methods: {
		// 拨打电话
		makePhoneCall() {
			uni.makePhoneCall({
				phoneNumber: '15366668888',
				success: function () {
					console.log('拨打电话成功');
				},
				fail: function () {
					console.log('拨打电话失败');
					uni.showToast({
						title: '拨打电话失败',
						icon: 'none'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.contact-page {
	min-height: 100vh;
	position: relative;
	background-color: #ffffff;
}

/* 背景图片 */
.background-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 490rpx;
	z-index: 1;
}

/* 内容容器 */
.content-container {
	position: relative;
	z-index: 2;
	padding-top: 250rpx; /* 为导航栏和背景图留出空间 */
	padding-left: 60rpx;
	padding-right: 60rpx;
}

/* 标题区域 */
.title-section {
	margin-bottom: 80rpx;
}

.main-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #000000;
	margin-bottom: 20rpx;
}

.sub-title {
	display: block;
	font-size: 28rpx;
	color: #999999;
}

/* 二维码区域 */
.qr-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 100rpx;
}

.qr-code-container {
	margin-bottom: 40rpx;
}

.qr-code-placeholder {
	width: 320rpx;
	height: 320rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	/* 可以添加一些样式来模拟二维码 */
	border: 2rpx solid #e0e0e0;
}

.qr-description {
	background-color: rgba(0, 0, 0, 0.6);
	padding: 16rpx 32rpx;
	border-radius: 40rpx;
}

.qr-text {
	font-size: 28rpx;
	color: #ffffff;
}

/* 联系方式区域 */
.contact-info-section {
	background-color: #F5F5F5;
	border-radius: 20rpx;
	overflow: hidden;
	margin: 0 40rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	padding: 40rpx 48rpx;
	border-bottom: 1rpx solid #E0E0E0;
}

.contact-item:last-child {
	border-bottom: none;
}

.contact-label {
	width: 120rpx;
}

.label-text {
	font-size: 32rpx;
	color: #666666;
}

.contact-value {
	flex: 1;
	display: flex;
    justify-content: flex-end;
    align-items: center;
}

.value-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
	margin-right: 20rpx;
}

.phone-icon-wrapper {
	padding: 10rpx;
	cursor: pointer;
}

.phone-icon {
	width: 40rpx;
	height: 40rpx;
}
</style>
