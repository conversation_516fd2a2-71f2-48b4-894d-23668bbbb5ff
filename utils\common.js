/**
 * 常用工具方法
 */

/**
 * 格式化金额
 * @param {number|string} amount 金额
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的金额
 */
export const formatMoney = (amount, decimals = 2) => {
	if (amount === null || amount === undefined || amount === '') {
		return '0.00';
	}
	
	const num = parseFloat(amount);
	if (isNaN(num)) {
		return '0.00';
	}
	
	return num.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

/**
 * 格式化日期时间
 * @param {Date|string|number} date 日期
 * @param {string} format 格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期
 */
export const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
	if (!date) return '';
	
	const d = new Date(date);
	if (isNaN(d.getTime())) return '';
	
	const year = d.getFullYear();
	const month = String(d.getMonth() + 1).padStart(2, '0');
	const day = String(d.getDate()).padStart(2, '0');
	const hours = String(d.getHours()).padStart(2, '0');
	const minutes = String(d.getMinutes()).padStart(2, '0');
	const seconds = String(d.getSeconds()).padStart(2, '0');
	
	return format
		.replace('YYYY', year)
		.replace('MM', month)
		.replace('DD', day)
		.replace('HH', hours)
		.replace('mm', minutes)
		.replace('ss', seconds);
};

/**
 * 获取相对时间
 * @param {Date|string|number} date 日期
 * @returns {string} 相对时间描述
 */
export const getRelativeTime = (date) => {
	if (!date) return '';
	
	const now = new Date();
	const target = new Date(date);
	const diff = now.getTime() - target.getTime();
	
	const minute = 60 * 1000;
	const hour = 60 * minute;
	const day = 24 * hour;
	const month = 30 * day;
	const year = 365 * day;
	
	if (diff < minute) {
		return '刚刚';
	} else if (diff < hour) {
		return Math.floor(diff / minute) + '分钟前';
	} else if (diff < day) {
		return Math.floor(diff / hour) + '小时前';
	} else if (diff < month) {
		return Math.floor(diff / day) + '天前';
	} else if (diff < year) {
		return Math.floor(diff / month) + '个月前';
	} else {
		return Math.floor(diff / year) + '年前';
	}
};

/**
 * 手机号脱敏
 * @param {string} phone 手机号
 * @returns {string} 脱敏后的手机号
 */
export const maskPhone = (phone) => {
	if (!phone || phone.length !== 11) {
		return phone;
	}
	return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

/**
 * 身份证号脱敏
 * @param {string} idCard 身份证号
 * @returns {string} 脱敏后的身份证号
 */
export const maskIdCard = (idCard) => {
	if (!idCard || idCard.length < 8) {
		return idCard;
	}
	const start = idCard.substring(0, 4);
	const end = idCard.substring(idCard.length - 4);
	const middle = '*'.repeat(idCard.length - 8);
	return start + middle + end;
};

/**
 * 验证手机号
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
export const validatePhone = (phone) => {
	const reg = /^1[3-9]\d{9}$/;
	return reg.test(phone);
};

/**
 * 验证邮箱
 * @param {string} email 邮箱
 * @returns {boolean} 是否有效
 */
export const validateEmail = (email) => {
	const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
	return reg.test(email);
};

/**
 * 验证身份证号
 * @param {string} idCard 身份证号
 * @returns {boolean} 是否有效
 */
export const validateIdCard = (idCard) => {
	const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
	return reg.test(idCard);
};

/**
 * 生成随机字符串
 * @param {number} length 长度
 * @returns {string} 随机字符串
 */
export const randomString = (length = 8) => {
	const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
	let result = '';
	for (let i = 0; i < length; i++) {
		result += chars.charAt(Math.floor(Math.random() * chars.length));
	}
	return result;
};

/**
 * 深拷贝
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export const deepClone = (obj) => {
	if (obj === null || typeof obj !== 'object') {
		return obj;
	}
	
	if (obj instanceof Date) {
		return new Date(obj.getTime());
	}
	
	if (obj instanceof Array) {
		return obj.map(item => deepClone(item));
	}
	
	if (typeof obj === 'object') {
		const cloned = {};
		for (let key in obj) {
			if (obj.hasOwnProperty(key)) {
				cloned[key] = deepClone(obj[key]);
			}
		}
		return cloned;
	}
	
	return obj;
};

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, delay = 300) => {
	let timer = null;
	return function(...args) {
		if (timer) clearTimeout(timer);
		timer = setTimeout(() => {
			func.apply(this, args);
		}, delay);
	};
};

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, delay = 300) => {
	let timer = null;
	return function(...args) {
		if (!timer) {
			timer = setTimeout(() => {
				func.apply(this, args);
				timer = null;
			}, delay);
		}
	};
};

/**
 * 获取URL参数
 * @param {string} name 参数名
 * @param {string} url URL地址，默认当前页面
 * @returns {string|null} 参数值
 */
export const getUrlParam = (name, url = window.location.href) => {
	const regex = new RegExp('[?&]' + name + '=([^&#]*)', 'i');
	const match = regex.exec(url);
	return match ? decodeURIComponent(match[1]) : null;
};

/**
 * 页面跳转
 * @param {string} url 页面路径
 * @param {object} params 参数
 * @param {string} type 跳转类型：navigate, redirect, reLaunch, switchTab
 */
export const navigateTo = (url, params = {}, type = 'navigate') => {
	// 构建参数字符串
	const paramStr = Object.keys(params)
		.map(key => `${key}=${encodeURIComponent(params[key])}`)
		.join('&');
	
	const fullUrl = paramStr ? `${url}?${paramStr}` : url;
	
	switch (type) {
		case 'redirect':
			uni.redirectTo({ url: fullUrl });
			break;
		case 'reLaunch':
			uni.reLaunch({ url: fullUrl });
			break;
		case 'switchTab':
			uni.switchTab({ url: fullUrl });
			break;
		default:
			uni.navigateTo({ url: fullUrl });
	}
};

/**
 * 显示提示信息
 * @param {string} title 提示内容
 * @param {string} icon 图标类型
 * @param {number} duration 显示时长
 */
export const showToast = (title, icon = 'none', duration = 2000) => {
	uni.showToast({
		title,
		icon,
		duration
	});
};

/**
 * 显示确认对话框
 * @param {string} content 内容
 * @param {string} title 标题
 * @returns {Promise<boolean>} 用户选择结果
 */
export const showConfirm = (content, title = '提示') => {
	return new Promise((resolve) => {
		uni.showModal({
			title,
			content,
			success: (res) => {
				resolve(res.confirm);
			}
		});
	});
};

export default {
	formatMoney,
	formatDate,
	getRelativeTime,
	maskPhone,
	maskIdCard,
	validatePhone,
	validateEmail,
	validateIdCard,
	randomString,
	deepClone,
	debounce,
	throttle,
	getUrlParam,
	navigateTo,
	showToast,
	showConfirm
};
