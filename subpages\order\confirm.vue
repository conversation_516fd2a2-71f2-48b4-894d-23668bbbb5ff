<template>
	<view class="order-confirm-page">
		<!-- 收货地址模块 -->
		<view class="address-section">
			<!-- 已选择地址 -->
			<view v-if="selectedAddress" class="selected-address" @click="selectAddress">
				<view class="address-info">
					<view class="address-header">
						<text class="receiver-name">{{ selectedAddress.name }}</text>
						<text class="receiver-phone">{{ selectedAddress.phone }}</text>
						<text v-if="selectedAddress.isDefault" class="default-tag">默认</text>
					</view>
					<view class="address-detail">
						<text class="address-text">{{ selectedAddress.detail }}</text>
					</view>
				</view>
				<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
			</view>

			<!-- 未选择地址 -->
			<view v-else class="add-address-btn" @click="selectAddress">
				<text class="add-address-text">选择收货地址</text>
				<uni-icons type="arrowright" size="16" color="#333"></uni-icons>
			</view>
		</view>
		
		<!-- 商品信息模块 -->
		<view class="product-section">
			<view class="section-header">
				<text class="section-title">商品信息</text>
			</view>
			
			<view class="product-item">
				<view class="product-image">
					<image :src="productInfo.image" mode="aspectFill" class="product-img"></image>
				</view>
				
				<view class="product-details">
					<view class="product-title">
						<text class="title-text">{{ productInfo.name }}</text>
					</view>
					
					<view class="product-price-row">
						<view class="price-wrapper">
							<text class="price-symbol">¥</text>
							<text class="price-amount">{{ productInfo.price }}</text>
						</view>
						
						<view class="quantity-counter">
							<view class="counter-btn" @click="decreaseQuantity">
								<text class="counter-text">-</text>
							</view>
							<view class="counter-number">
								<text class="number-text">{{ quantity }}</text>
							</view>
							<view class="counter-btn" @click="increaseQuantity">
								<text class="counter-text">+</text>
							</view>
						</view>
					</view>
					
					<view class="subtotal-row">
						<text class="subtotal-label">合计：</text>
						<text class="subtotal-amount">¥{{ totalAmount }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 价格明细模块 -->
		<view class="price-detail-section">
			<view class="section-header">
				<text class="section-title">价格明细</text>
			</view>

			<view class="price-item">
				<text class="price-label">商品金额</text>
				<text class="price-value">¥{{ totalAmount }}</text>
			</view>

			<view class="price-item">
				<text class="price-label">运费</text>
				<text class="price-value-normal">¥{{ shippingFee }}</text>
			</view>

			<view class="price-item total-item">
				<text class="price-label">合计</text>
				<text class="price-value">¥{{ finalAmount }}</text>
			</view>
		</view>
		
		<!-- 订单备注模块 -->
		<view class="remark-section">
			<view class="remark-input-wrapper">
				<textarea
					class="remark-input"
					placeholder="订单备注"
					v-model="orderRemark"
					maxlength="200">
				</textarea>
			</view>
		</view>
		
		<!-- 底部结算区域 -->
		<view class="bottom-settlement">
			<view class="payment-info">
				<text class="payment-label">实付款：</text>
				<text class="payment-amount">¥{{ finalAmount }}</text>
			</view>
			
			<view class="submit-btn" @click="submitOrder">
				<text class="submit-text">提交订单</text>
			</view>
		</view>
	</view>
</template>

<script>
import { goodsApi } from '@/api/index.js'

export default {
	name: 'OrderConfirm',
	data() {
		return {
			goodsId: '', // 商品ID
			productInfo: {
				id: '',
				name: '',
				price: 0,
				image: '',
				specification: '',
				freight: 0
			},
			quantity: 1,
			shippingFee: 0,
			orderRemark: '',
			selectedAddress: null // 选中的收货地址
		}
	},

	onLoad(options) {
		// 处理从商品详情页传递的商品数据
		if (options.productData) {
			try {
				const productData = JSON.parse(decodeURIComponent(options.productData));
				this.goodsId = productData.goodsId;
				this.productInfo = {
					id: productData.id,
					name: productData.name,
					price: productData.price,
					image: productData.image,
					specification: productData.specification,
					freight: productData.freight || 0
				};
				this.shippingFee = productData.freight || 0;

				// 调用商品详情接口获取最新数据
				this.loadProductDetail();
			} catch (error) {
				console.error('解析商品数据失败:', error);
			}
		}
	},
	computed: {
		totalAmount() {
			return (this.productInfo.price * this.quantity).toFixed(2);
		},
		finalAmount() {
			return (parseFloat(this.totalAmount) + this.shippingFee).toFixed(2);
		}
	},
	onShow() {
		// 页面显示时，地址数据会通过地址列表页面直接设置到 this.selectedAddress
		// 无需额外处理
	},

	methods: {
		// 加载商品详情
		async loadProductDetail() {
			if (!this.goodsId) return;

			try {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});

				const result = await goodsApi.getGoodsDetail(this.goodsId);

				uni.hideLoading();

				if (result.code === 0 || result.code === 200) {
					const goodsData = result.data.result;

					// 更新商品信息
					this.productInfo = {
						id: goodsData.id,
						name: goodsData.goodsName,
						price: goodsData.price,
						image: goodsData.goodsImg || this.productInfo.image,
						specification: goodsData.specification,
						freight: goodsData.freight || 0
					};

					// 更新运费
					this.shippingFee = goodsData.freight || 0;
				}
			} catch (error) {
				uni.hideLoading();
				console.error('加载商品详情失败:', error);
			}
		},

		// 选择收货地址
		selectAddress() {
			uni.navigateTo({
				url: '/subpages/address/list?from=order'
			});
		},
		
		// 减少数量
		decreaseQuantity() {
			if (this.quantity > 1) {
				this.quantity--;
			}
		},
		
		// 增加数量
		increaseQuantity() {
			this.quantity++;
		},
		
		// 提交订单
		async submitOrder() {
			// 验证是否选择了收货地址
			if (!this.selectedAddress) {
				uni.showToast({
					title: '请选择收货地址',
					icon: 'none'
				});
				return;
			}

			// 验证商品信息
			if (!this.goodsId || !this.productInfo.id) {
				uni.showToast({
					title: '商品信息异常，请重试',
					icon: 'none'
				});
				return;
			}

			try {
				uni.showLoading({
					title: '提交订单中...',
					mask: true
				});

				// 构造下单参数
				const orderParams = {
					addressId: this.selectedAddress.id,
					goodsId: this.goodsId,
					goodsNum: this.quantity,
					pickStore: "1", // 自提门店，如果不需要可以为空
					reference: this.orderRemark || "", // 订单备注
					type: "1" // 订单类型，可以根据业务需求调整
				};

				console.log('下单参数:', orderParams);

				const result = await goodsApi.settleOrder(orderParams);

				uni.hideLoading();

				if (result.code === 0 || result.code === 200) {
					uni.showToast({
						title: '下单成功',
						icon: 'success'
					});

					// 下单成功后跳转到支付页面或订单详情页面
					setTimeout(() => {
						if (result.data && result.data.orderId) {
							// 如果返回订单ID，跳转到支付页面
							uni.navigateTo({
								url: `/subpages/payment/pay?orderId=${result.data.orderId}&amount=${this.finalAmount}`
							});
						} else {
							// 否则跳转到订单列表
							uni.navigateTo({
								url: '/subpages/order/list'
							});
						}
					}, 1500);
				} else {
					uni.showToast({
						title: result.msg || '下单失败，请重试',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('下单失败:', error);
				uni.showToast({
					title: '下单失败，请重试',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style scoped>
.order-confirm-page {
	background-color: #F5F5F5;
	min-height: 100vh;
	padding: 20rpx;
	padding-bottom: 140rpx; /* 为底部结算区域留出空间 */
}

/* 添加收货地址模块 */
.address-section {
	background-color: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	padding: 10rpx 30rpx;
}

.add-address-btn {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 80rpx;
}

.add-address-text {
	color: #4D40E5;
	font-size: 32rpx;
}

/* 已选择地址样式 */
.selected-address {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 0;
}

.address-info {
	flex: 1;
}

.address-header {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.receiver-name {
	color: #333333;
	font-size: 32rpx;
	font-weight: bold;
	margin-right: 20rpx;
}

.receiver-phone {
	color: #666666;
	font-size: 28rpx;
	margin-right: 20rpx;
}

.default-tag {
	background-color: #4D40E5;
	color: #ffffff;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
}

.address-detail {
	margin-top: 10rpx;
}

.address-text {
	color: #666666;
	font-size: 28rpx;
	line-height: 1.4;
}

/* 通用模块样式 */
.product-section,
.price-detail-section,
.remark-section {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.section-header {
	margin-bottom: 30rpx;
	border-bottom: 1rpx solid #E5E5E5;
	padding-bottom: 20rpx;
}

.section-title {
	color: #333333;
	font-size: 32rpx;
	font-weight: bold;
}

/* 商品信息模块 */
.product-item {
	display: flex;
	align-items: flex-start;
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	margin-right: 20rpx;
	border-radius: 10rpx;
	overflow: hidden;
}

.product-img {
	width: 100%;
	height: 100%;
}

.product-details {
	flex: 1;
}

.product-title {
	margin-bottom: 20rpx;
}

.title-text {
	color: #333333;
	font-size: 28rpx;
	line-height: 1.4;
}

.product-price-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 60rpx;
}

.price-wrapper {
	display: flex;
	align-items: baseline;
}

.price-symbol {
	color: #FF2222;
	font-size: 24rpx;
	font-weight: bold;
}

.price-amount {
	color: #FF2222;
	font-size: 32rpx;
	font-weight: bold;
}

/* 计数器样式 */
.quantity-counter {
	display: flex;
	align-items: center;
	border: 1rpx solid #E5E5E5;
	border-radius: 20rpx;
	overflow: hidden;
}

.counter-btn {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.counter-btn:active {
	background-color: #E5E5E5;
}

.counter-text {
	color: #666666;
	font-size: 32rpx;
	font-weight: bold;
}

.counter-number {
	width: 60rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #ffffff;
}

.number-text {
	color: #333333;
	font-size: 28rpx;
}

.subtotal-row {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.subtotal-label {
	color: #333333;
	font-size: 28rpx;
	margin-right: 10rpx;
}

.subtotal-amount {
	color: #FF2222;
	font-size: 32rpx;
	font-weight: bold;
}

/* 价格明细模块 */
.price-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30rpx;
}

.price-item:last-child {
	margin-bottom: 0;
}

.price-label {
	color: #666666;
	font-size: 28rpx;
}

.price-value {
	color: #FF2222;
	font-size: 32rpx;
	font-weight: bold;
}

.price-value-normal {
	color: #333333;
	font-size: 28rpx;
}

.total-item {
	border-top: 1rpx solid #E5E5E5;
	padding-top: 20rpx;
	margin-top: 20rpx;
}

/* 订单备注模块 */
.remark-input-wrapper {
	border: 1rpx solid #E5E5E5;
	border-radius: 10rpx;
	padding: 20rpx;
}

.remark-input {
	width: 100%;
	height: 60rpx;
	font-size: 28rpx;
	color: #333333;
	line-height: 1.4;
}

/* 底部结算区域 */
.bottom-settlement {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	padding: 40rpx 30rpx 20rpx 30rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	display: flex;
	align-items: center;
	justify-content: flex-end;
	border-radius: 20rpx 20rpx 0 0;
}

.payment-info {
	display: flex;
	align-items: baseline;
	margin-right: 20rpx;
}

.payment-label {
	color: #333333;
	font-size: 28rpx;
	margin-right: 10rpx;
}

.payment-amount {
	color: #FF2222;
	font-size: 36rpx;
	font-weight: bold;
}

.submit-btn {
	background-color: #4D40E5;
	border-radius: 20rpx;
	padding: 20rpx 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: bold;
}
</style>
