<template>
	<view class="earnings-page">

		<!-- Tab栏 -->
		<view class="tab-container">
			<view class="tab-item"
				  v-for="(tab, index) in tabs"
				  :key="index"
				  :class="{ active: currentTab === index }"
				  @click="switchTab(index)">
				<text class="tab-text">{{ tab.name }}</text>
				<image v-if="currentTab === index"
					   class="tab-indicator"
					   src="/static/images/icon_option_select.png"
					   mode="aspectFit"></image>
			</view>
		</view>

		<!-- 收益展示卡片 -->
		<view class="earnings-card">
			<view class="earnings-content">
				<view class="earnings-left">
					<text class="earnings-label">累计分佣(元)</text>
					<text class="earnings-amount">{{ allFenrun.toFixed(2) }}</text>
					<view class="earnings-detail">
						<view class="detail-item">
							<text class="detail-label">今日分佣(元)</text>
							<text class="detail-value">+{{ dayFenrun.toFixed(2) }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">月度分佣(元)</text>
							<text class="detail-value">+{{ monthsFenrun.toFixed(2) }}</text>
						</view>
					</view>
				</view>
				<image class="earnings-icon"
					   src="/static/images/img_ceding_index.png"
					   mode="aspectFit"></image>
			</view>
		</view>

		<!-- 收益记录 -->
		<view class="record-container">
			<view class="record-header">
				<view class="date-selector" @click="showDatePicker">
					<text class="date-text">{{ selectedDate }}</text>
					<uni-icons type="down" size="16" color="#333"></uni-icons>
				</view>
			</view>

			<view class="record-stats">
				<view class="stat-item">
					<text class="stat-label">订单实收(元)：</text>
					<text class="stat-value">¥{{ trxAmt.toFixed(2) }}</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">交易笔数(笔)：</text>
					<text class="stat-value">{{ trxNum }}笔</text>
				</view>
			</view>

			<!-- 收益记录列表 -->
			<view class="record-list">
				<view class="record-item" v-for="(record, index) in recordList" :key="index">
					<view class="record-left">
						<view class="record-title-row">
							<text class="record-title">{{ record.title }}</text>
							<text v-if="record.tag" class="record-tag">{{ record.tag }}</text>
						</view>
						<text class="record-time">{{ record.time }}</text>
					</view>
					<view class="record-right">
						<text class="record-amount" :class="{ negative: record.amount < 0 }">
							{{ record.amount > 0 ? '+' : '' }}¥{{ Math.abs(record.amount).toFixed(2) }}
						</text>
						<text class="record-sales">销售额：¥{{ record.sales }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 时间选择弹窗 -->
		<uni-popup ref="datePopup" type="bottom" background-color="#fff">
			<view class="date-popup-content">
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<view class="tab-selector">
						<view class="tab-option"
							  :class="{ active: dateTabIndex === 0 }"
							  @click="switchDateTab(0)">
							<text class="tab-option-text">月份选择</text>
						</view>
						<view class="tab-option"
							  :class="{ active: dateTabIndex === 1 }"
							  @click="switchDateTab(1)">
							<text class="tab-option-text">自定义选择</text>
						</view>
					</view>
					<view class="close-btn" @click="closePopup">
						<uni-icons type="close" size="20" color="#999"></uni-icons>
					</view>
				</view>

				<!-- 月份选择内容 -->
				<view v-if="dateTabIndex === 0" class="month-selector">
					<picker-view
						class="date-picker-view"
						:value="datePickerValue"
						:key="datePickerKey"
						@change="onDatePickerChange">
						<picker-view-column>
							<view class="picker-item" v-for="(year, index) in years" :key="index">
								<text>{{ year }}年</text>
							</view>
						</picker-view-column>
						<picker-view-column>
							<view class="picker-item" v-for="(month, index) in months" :key="index">
								<text>{{ month }}月</text>
							</view>
						</picker-view-column>
						<picker-view-column>
							<view class="picker-item" v-for="(day, index) in days" :key="index">
								<text>{{ day }}日</text>
							</view>
						</picker-view-column>
					</picker-view>
				</view>

				<!-- 自定义选择内容 -->
				<view v-if="dateTabIndex === 1" class="custom-selector">
					<!-- 日期范围选择器 -->
					<view class="date-range-header">
						<view class="date-input-group">
							<text class="date-input"
								  :class="{ active: isSelectingStartDate }"
								  @click="selectStartDate">{{ startDate || '2025-05-11' }}</text>
						</view>
						<text class="date-separator">至</text>
						<view class="date-input-group">
							<text class="date-input"
								  :class="{ active: !isSelectingStartDate }"
								  @click="selectEndDate">{{ endDate || '结束时间' }}</text>
						</view>
					</view>

					<!-- 日期选择器 -->
					<picker-view
						class="custom-date-picker-view"
						:value="customDatePickerValue"
						:key="customDatePickerKey"
						@change="onCustomDatePickerChange">
						<picker-view-column>
							<view class="picker-item" v-for="(year, index) in years" :key="index">
								<text>{{ year }}</text>
							</view>
						</picker-view-column>
						<picker-view-column>
							<view class="picker-item" v-for="(month, index) in months" :key="index">
								<text>{{ month }}</text>
							</view>
						</picker-view-column>
						<picker-view-column>
							<view class="picker-item" v-for="(day, index) in customDays" :key="index">
								<text>{{ day }}</text>
							</view>
						</picker-view-column>
					</picker-view>
				</view>

				<!-- 提交按钮 -->
				<button class="submit-btn" @click="confirmDate">提交</button>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { earningsApi } from '@/api/index.js';

	export default {
		data() {
			return {
				currentTab: 0,
				tabs: [
					{ name: '全部' },
					{ name: '商品分佣' },
					{ name: '码牌分佣' },
					// { name: '爆品活动' }
				],
				selectedDate: '',
				// 收益统计数据
				allFenrun: 0, // 累计分佣
				dayFenrun: 0, // 今日分佣
				monthsFenrun: 0, // 月度分佣
				trxAmt: 0, // 订单实收
				trxNum: 0, // 交易笔数
				dateTabIndex: 0, // 0: 月份选择, 1: 自定义选择
				customDate: '',
				// 日期选择器相关数据
				years: [],
				months: [],
				days: [],
				datePickerValue: [0, 0, 0], // 年月日的索引
				datePickerKey: 0, // 用于强制重新渲染picker-view
				// 自定义选择相关数据
				startDate: '',
				endDate: '',
				isSelectingStartDate: true, // true: 选择开始日期, false: 选择结束日期
				customDays: [],
				customDatePickerValue: [0, 0, 0],
				customDatePickerKey: 0,
				recordList: [
					{
						title: '202506101113655',
						time: '07-11 10:11',
						amount: 3.00,
						sales: '100.00',
						tag: ''
					},
					{
						title: '202506101113655',
						time: '07-11 10:10',
						amount: -3.00,
						sales: '100.00',
						tag: '退货退款'
					},
					{
						title: '码牌',
						time: '07-11 09:12',
						amount: 3.00,
						sales: '100.00',
						tag: ''
					},
					{
						title: '202506101113655',
						time: '07-11 08:12',
						amount: -3.00,
						sales: '100.00',
						tag: '退货退款'
					},
					{
						title: '202506101113655',
						time: '07-11 08:10',
						amount: 3.00,
						sales: '100.00',
						tag: ''
					},
					{
						title: '202506101113655',
						time: '07-11 08:09',
						amount: 3.00,
						sales: '100.00',
						tag: ''
					}
				]
			}
		},
		onLoad() {
			this.initDatePicker();
			this.loadEarningsList();
		},
		onShow() {
			this.loadEarningsList();
		},
		methods: {
			// 加载收益明细列表
			async loadEarningsList() {
				try {
					uni.showLoading({
						title: '加载中...',
						mask: true
					});

					// 构建请求参数
					const params = {
						page: 1,
						limit: 20,
						syType: this.getSyType(), // 收益类型：0全部 1商品分佣 2码牌分佣
						time: this.getTimeParam(), // 时间参数
						timePeriod: this.getTimePeriodParam() // 时间段参数
					};

					console.log('请求收益明细参数:', params);

					const result = await earningsApi.getEarningsList(params);

					uni.hideLoading();

					console.log('收益明细响应数据:', result);

					if (result.code === 0 || result.code === 200) {
						// 映射收益数据
						this.mapEarningsData(result.data);
					} else {
						uni.showToast({
							title: result.msg || '加载失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('加载收益明细失败:', error);
					uni.showToast({
						title: '加载失败，请重试',
						icon: 'none'
					});
				}
			},

			// 获取收益类型参数
			getSyType() {
				// 根据当前Tab返回对应的syType
				// 0:全部、1:商品分佣、2:码牌分佣、3:爆品活动(暂时映射为0)
				switch (this.currentTab) {
					case 0: return 0; // 全部
					case 1: return 1; // 商品分佣
					case 2: return 2; // 码牌分佣
					case 3: return 0; // 爆品活动，暂时映射为全部
					default: return 0;
				}
			},

			// 获取时间参数
			getTimeParam() {
				if (!this.selectedDate) return '';

				// 如果是日期范围格式（包含"至"），返回空字符串，使用timePeriod
				if (this.selectedDate.includes('至')) {
					return '';
				}

				// 单个日期格式，直接返回
				return this.selectedDate;
			},

			// 获取时间段参数
			getTimePeriodParam() {
				if (!this.selectedDate) return '';

				// 如果是日期范围格式（包含"至"）
				if (this.selectedDate.includes('至')) {
					// 将"至"替换为"-"
					return this.selectedDate.replace('至', ' - ');
				}

				// 单个日期格式，返回空字符串
				return '';
			},

			// 映射收益数据
			mapEarningsData(data) {
				// 更新收益统计数据
				this.updateEarningsStats(data);

				// 更新收益记录列表
				this.updateRecordList(data.page.records);
			},

			// 更新收益统计数据
			updateEarningsStats(data) {
				// 更新累计分佣
				this.allFenrun = data.allFenrun || 0;

				// 更新今日分佣
				this.dayFenrun = data.dayFenrun || 0;

				// 更新月度分佣
				this.monthsFenrun = data.monthsFenrun || 0;

				// 更新订单实收
				this.trxAmt = data.trxAmt || 0;

				// 更新交易笔数
				this.trxNum = data.trxNum || 0;
			},

			// 更新收益记录列表
			updateRecordList(records) {
				this.recordList = (records || []).map(record => {
					// 根据trxAmt的正负来判断分佣金额的正负
					const isNegative = (record.trxAmt || 0) < 0;
					const fenrunAmount = record.fenrunAmt || 0;

					return {
						id: record.id,
						title: this.getRecordTitle(record),
						time: this.formatRecordTime(record.createDate),
						amount: isNegative ? -Math.abs(fenrunAmount) : Math.abs(fenrunAmount), // 根据交易金额正负确定分佣金额正负
						sales: Math.abs(record.trxAmt || 0).toFixed(2), // 销售额（取绝对值）
						tag: record.remarks || '', // 备注作为标签
						type: record.type, // 类型
						fenrunType: record.fenrunType // 分佣类型
					};
				});
			},

			// 获取记录标题
			getRecordTitle(record) {
				// 根据type字段确定标题
				switch (record.type) {
					case '1': return record.orderNo || '商品订单';
					case '2': return '码牌';
					default: return record.orderNo || '未知类型';
				}
			},

			// 格式化记录时间
			formatRecordTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${month}-${day} ${hours}:${minutes}`;
			},

			goBack() {
				uni.navigateBack();
			},
			switchTab(index) {
				this.currentTab = index;
				// 切换Tab时重新加载数据
				this.loadEarningsList();
			},
			showDatePicker() {
				// 打开弹窗
				this.$refs.datePopup.open();
				// 等待弹窗打开后再重置日期选择器的值
				this.$nextTick(() => {
					this.resetDatePickerValue();
				});
			},

			// 重置日期选择器的值为当前选中日期
			resetDatePickerValue() {
				// 解析当前选中的日期
				let year, month, day;

				if (this.selectedDate) {
					const dateParts = this.selectedDate.split('-');
					year = parseInt(dateParts[0]);
					month = parseInt(dateParts[1]);
					day = parseInt(dateParts[2]);
				} else {
					// 如果没有选中日期，使用当前日期
					const now = new Date();
					year = now.getFullYear();
					month = now.getMonth() + 1;
					day = now.getDate();
					this.selectedDate = this.formatDate(year, month, day);
				}

				// 更新日期数组
				this.updateDays(year, month);

				// 使用 $nextTick 确保数据更新后再设置选中值
				this.$nextTick(() => {
					// 设置选中值
					const yearIndex = this.years.findIndex(y => y === year);
					const monthIndex = this.months.findIndex(m => m === month);
					const dayIndex = this.days.findIndex(d => d === day);

					// 确保索引有效
					this.datePickerValue = [
						yearIndex > -1 ? yearIndex : 0,
						monthIndex > -1 ? monthIndex : 0,
						dayIndex > -1 ? dayIndex : 0
					];

					// 更新key强制重新渲染
					this.datePickerKey++;
				});
			},
			closePopup() {
				this.$refs.datePopup.close();
			},
			switchDateTab(index) {
				this.dateTabIndex = index;
				// 切换到自定义选择时，默认选择开始日期
				if (index === 1) {
					this.isSelectingStartDate = true;
					this.$nextTick(() => {
						this.resetCustomDatePickerValue();
					});
				}
			},
			// 初始化日期选择器
			initDatePicker() {
				const currentDate = new Date();
				const currentYear = currentDate.getFullYear();
				const currentMonth = currentDate.getMonth() + 1;
				const currentDay = currentDate.getDate();

				// 初始化年份数组（当前年份前后各5年）
				this.years = [];
				for (let i = currentYear - 5; i <= currentYear + 5; i++) {
					this.years.push(i);
				}

				// 初始化月份数组
				this.months = [];
				for (let i = 1; i <= 12; i++) {
					this.months.push(i);
				}

				// 初始化日期数组
				this.updateDays(currentYear, currentMonth);
				this.updateCustomDays(currentYear, currentMonth);

				// 设置默认选中当前日期
				this.selectedDate = this.formatDate(currentYear, currentMonth, currentDay);
				this.customDate = this.selectedDate;
				this.startDate = this.selectedDate;

				// 初始化日期选择器的值
				this.resetDatePickerValue();
				this.resetCustomDatePickerValue();
			},
			// 更新日期数组
			updateDays(year, month) {
				const daysInMonth = new Date(year, month, 0).getDate();
				this.days = [];
				for (let i = 1; i <= daysInMonth; i++) {
					this.days.push(i);
				}
			},
			// 更新自定义选择的日期数组
			updateCustomDays(year, month) {
				const daysInMonth = new Date(year, month, 0).getDate();
				this.customDays = [];
				for (let i = 1; i <= daysInMonth; i++) {
					this.customDays.push(i);
				}
			},
			// 日期选择器变化事件
			onDatePickerChange(e) {
				const [yearIndex, monthIndex, dayIndex] = e.detail.value;
				const selectedYear = this.years[yearIndex];
				const selectedMonth = this.months[monthIndex];

				// 更新日期数组
				this.updateDays(selectedYear, selectedMonth);

				// 如果当前选中的日期超出了新月份的天数，调整到最后一天
				const maxDay = this.days.length;
				const adjustedDayIndex = dayIndex >= maxDay ? maxDay - 1 : dayIndex;

				// 更新日期选择器的值
				this.datePickerValue = [yearIndex, monthIndex, adjustedDayIndex];

				// 如果日期被调整了，需要在下一个tick更新picker-view
				if (adjustedDayIndex !== dayIndex) {
					this.$nextTick(() => {
						this.datePickerValue = [yearIndex, monthIndex, adjustedDayIndex];
					});
				}
			},
			// 格式化日期
			formatDate(year, month, day) {
				return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
			},
			// 选择开始日期
			selectStartDate() {
				if (!this.isSelectingStartDate) {
					this.isSelectingStartDate = true;
					this.resetCustomDatePickerValue();
				}
			},
			// 选择结束日期
			selectEndDate() {
				if (this.isSelectingStartDate) {
					this.isSelectingStartDate = false;
					this.resetCustomDatePickerValue();
				}
			},
			// 重置自定义日期选择器的值
			resetCustomDatePickerValue() {
				// 根据当前选择的是开始还是结束日期来设置默认值
				let targetDate = this.isSelectingStartDate ? this.startDate : this.endDate;

				if (!targetDate) {
					// 如果没有目标日期，使用当前日期
					const now = new Date();
					const year = now.getFullYear();
					const month = now.getMonth() + 1;
					const day = now.getDate();
					targetDate = this.formatDate(year, month, day);
				}

				const dateParts = targetDate.split('-');
				const year = parseInt(dateParts[0]);
				const month = parseInt(dateParts[1]);
				const day = parseInt(dateParts[2]);

				// 更新自定义日期数组
				this.updateCustomDays(year, month);

				this.$nextTick(() => {
					// 设置选中值
					const yearIndex = this.years.findIndex(y => y === year);
					const monthIndex = this.months.findIndex(m => m === month);
					const dayIndex = this.customDays.findIndex(d => d === day);

					this.customDatePickerValue = [
						yearIndex > -1 ? yearIndex : 0,
						monthIndex > -1 ? monthIndex : 0,
						dayIndex > -1 ? dayIndex : 0
					];

					this.customDatePickerKey++;
				});
			},
			// 自定义日期选择器变化事件
			onCustomDatePickerChange(e) {
				const [yearIndex, monthIndex, dayIndex] = e.detail.value;
				const selectedYear = this.years[yearIndex];
				const selectedMonth = this.months[monthIndex];

				// 更新自定义日期数组
				this.updateCustomDays(selectedYear, selectedMonth);

				// 如果当前选中的日期超出了新月份的天数，调整到最后一天
				const maxDay = this.customDays.length;
				const adjustedDayIndex = dayIndex >= maxDay ? maxDay - 1 : dayIndex;

				this.customDatePickerValue = [yearIndex, monthIndex, adjustedDayIndex];

				// 更新对应的日期
				const selectedDay = this.customDays[adjustedDayIndex];
				const formattedDate = this.formatDate(selectedYear, selectedMonth, selectedDay);

				if (this.isSelectingStartDate) {
					this.startDate = formattedDate;
				} else {
					this.endDate = formattedDate;
				}

				if (adjustedDayIndex !== dayIndex) {
					this.$nextTick(() => {
						this.customDatePickerValue = [yearIndex, monthIndex, adjustedDayIndex];
					});
				}
			},
			onCustomDateChange(e) {
				this.customDate = e.detail.value;
			},
			confirmDate() {
				if (this.dateTabIndex === 0) {
					// 月份选择
					const [yearIndex, monthIndex, dayIndex] = this.datePickerValue;
					const selectedYear = this.years[yearIndex];
					const selectedMonth = this.months[monthIndex];
					const selectedDay = this.days[dayIndex];
					this.selectedDate = this.formatDate(selectedYear, selectedMonth, selectedDay);
				} else {
					// 自定义选择 - 显示年月日至年月日的格式
					if (this.startDate && this.endDate) {
						this.selectedDate = `${this.startDate}至${this.endDate}`;
					} else if (this.startDate) {
						// 如果只有开始日期，显示单个完整日期
						this.selectedDate = this.startDate;
					}
				}
				this.closePopup();
				// 日期变更后重新加载数据
				this.loadEarningsList();
			}
		}
	}
</script>
<style scoped>
	.earnings-page {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	/* 标题栏 */
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 44rpx 32rpx 20rpx;
		background-color: #fff;
	}

	.back-btn {
		width: 44rpx;
		height: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.back-icon {
		font-size: 36rpx;
		color: #333;
		font-weight: bold;
	}

	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}

	.more-btn {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.more-icon {
		font-size: 36rpx;
		color: #333;
		font-weight: bold;
	}

	.record-btn {
		width: 44rpx;
		height: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.record-icon {
		font-size: 32rpx;
		color: #333;
		border: 2rpx solid #333;
		border-radius: 50%;
		width: 32rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* Tab栏 */
	.tab-container {
		display: flex;
		padding: 0 32rpx;
	}

	.tab-item {
		flex: 1;
		padding: 24rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}

	.tab-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 8rpx;
	}

	.tab-item.active .tab-text {
		color: #333;
		font-weight: 600;
	}

	.tab-indicator {
		width: 150rpx;
		height: 30rpx;
		position: absolute;
		bottom: 0;
	}

	/* 收益展示卡片 */
	.earnings-card {
		margin: 24rpx 32rpx;
		border-radius: 24rpx;
		overflow: hidden;
		background-image: url('/static/images/tixianbg.jpg');
		background-size: cover;
		background-position: center;
	}

	.earnings-content {
		padding: 48rpx 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		position: relative;
	}

	.earnings-left {
		flex: 1;
	}

	.earnings-label {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
		display: block;
		margin-bottom: 16rpx;
	}

	.earnings-amount {
		font-size: 54rpx;
		font-weight: bold;
		color: #fff;
		display: block;
		margin-bottom: 32rpx;
	}

	.earnings-detail {
		display: flex;
		gap: 40rpx;
	}

	.detail-item {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.detail-label {
		width: 150rpx;
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
	}

	.detail-value {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 1);
		font-weight: 500;
	}

	.earnings-icon {
		width: 140rpx;
		height: 140rpx;
		margin-top: 20rpx;
		position: absolute;
		right: 50rpx;
		top: 0rpx;
	}

	/* 收益记录 */
	.record-container {
		margin: 0 32rpx;
		background-color: #fff;
		border-radius: 24rpx;
		padding: 32rpx;
	}

	.record-header {
		margin-bottom: 32rpx;
	}

	.date-selector {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.date-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.dropdown-icon {
		font-size: 24rpx;
		color: #666;
	}

	.record-stats {
		display: flex;
		justify-content: space-between;
		margin-bottom: 32rpx;
		padding-bottom: 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.stat-item {
		display: flex;
		align-items: center;
	}

	.stat-label {
		font-size: 26rpx;
		color: #666;
	}

	.stat-value {
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
	}

	/* 收益记录列表 */
	.record-list {
		margin-top: 16rpx;
	}

	.record-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #f8f8f8;
	}

	.record-item:last-child {
		border-bottom: none;
	}

	.record-left {
		flex: 1;
	}

	.record-title-row {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 8rpx;
	}

	.record-title {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}

	.record-tag {
		background-color: #f0f0f0;
		color: #666;
		font-size: 20rpx;
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
	}

	.record-time {
		font-size: 24rpx;
		color: #999;
	}

	.record-right {
		text-align: right;
	}

	.record-amount {
		font-size: 32rpx;
		color: #333;
		font-weight: 600;
		display: block;
		margin-bottom: 8rpx;
	}

	.record-amount.negative {
		color: #333;
	}

	.record-sales {
		font-size: 24rpx;
		color: #999;
	}

	/* 日期选择弹窗样式 */
	.date-popup-content {
		background-color: #fff;
		border-radius: 24rpx 24rpx 0 0;
		overflow: hidden;
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 30rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.tab-selector {
		display: flex;
		gap: 40rpx;
	}

	.tab-option {
		position: relative;
		padding-bottom: 10rpx;
	}

	.tab-option-text {
		font-size: 32rpx;
		color: #666;
	}

	.tab-option.active .tab-option-text {
		color: #6248ff;
		font-weight: 600;
	}

	.tab-option.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 4rpx;
		background-color: #6248ff;
		border-radius: 2rpx;
	}

	.close-btn {
		width: 44rpx;
		height: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 月份选择样式 */
	.month-selector {
		height: 500rpx;
		padding: 20rpx 0;
	}

	.date-picker-view {
		width: 100%;
		height: 100%;
	}

	.picker-item {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80rpx;
	}

	.picker-item text {
		font-size: 32rpx;
		color: #333;
	}

	/* 自定义选择样式 */
	.custom-selector {
		padding: 30rpx;
	}

	.date-range-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 40rpx;
		padding: 0 20rpx;
	}

	.date-input-group {
		flex: 1;
		display: flex;
		justify-content: center;
	}

	.date-input {
		font-size: 32rpx;
		color: #666666;
		padding-bottom: 8rpx;
		border-bottom: 2rpx solid #666666;
		min-width: 200rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.date-input.active {
		color: #6248ff;
		border-bottom-color: #6248ff;
	}

	.date-separator {
		font-size: 32rpx;
		color: #333;
		margin: 0 30rpx;
	}

	.custom-date-picker-view {
		width: 100%;
		height: 400rpx;
	}

	/* 提交按钮样式 */
	.submit-btn {
		margin: 30rpx;
		height: 88rpx;
		background-color: #6248ff;
		border-radius: 44rpx;
		color: #fff;
		font-size: 32rpx;
		font-weight: 500;
		border: none;
		outline: none;
	}

	.submit-btn::after {
		border: none;
	}
</style>
