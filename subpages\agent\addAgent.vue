<template>
	<view class="page">
		<view class="audit_top" v-if="valiFormData.id">
			<view style="display: flex;align-items: center;">
				<view style="display: flex;"><img style="width: 40rpx;height: 40rpx;" src="/static/images/icon_lose_result.png" alt="" /></view>
				<view style="margin-left: 20rpx;font-size: 30rpx;font-weight: 600;">审核失败</view>
			</view>
			<view style="color: #666666;font-size: 24rpx;margin: 10rpx 0 0 60rpx;">失败原因：{{valiFormData.denialReason||'-'}}</view>
		</view>
		<!-- 基础表单校验 -->
		<uni-forms ref="valiForm" :rules="rules" :border="true" :modelValue="valiFormData">
			<uni-section titleFontSize="32rpx" title="代理商主体信息" type="line">
				<view class="example">
					<uni-forms-item label="代理商账户类型" required name="type">
						<uni-data-checkbox v-model="valiFormData.type" :localdata="typeDict" @change="onChange" />
					</uni-forms-item>
					<view v-if="valiFormData.type==1" class="uni-forms-item" style="border-top: 1px #eee solid;padding-top: 20rpx;">
						<view class="uni-forms-item__label"><text class="is-required">*</text><text>请上传主体信息</text></view>
						<view style="margin-top: 30rpx;">
							<uni-grid :column="4" :show-border="false" :highlight="false">
								<uni-grid-item v-for="(item, index) in valiFormData.licenseUrl" :index="index" :key="index">
									<view class="grid-item-box" style="background-color: #fff;">
										<img style="width: 120rpx;height: 120rpx;" :src="item" alt="" />
										<img @click="delImages(item,'licenseUrl')" class="del_round" src="/static/images/close_icon.png" alt="" />
									</view>
								</uni-grid-item>
								<uni-grid-item>
									<view class="grid-item-box">
										<img @click="picUP('licenseUrl')" style="width: 120rpx;height: 120rpx;" src="/static/images/img_ phone.png" alt="" />
									</view>
								</uni-grid-item>
							</uni-grid>
						</view>
					</view>
					<uni-forms-item label="代理商层级" required name="agentGrade">
						<picker @change="agentGradeChange" :value="agentIndex" :range="agentGradeDict" range-key="text">
							<view style="display: flex;align-items: center;padding-right: 10rpx;">
								<view class="uni-input" :style="{color: valiFormData.agentGrade==''&&valiFormData.agentGrade!==0?'#999999':'',paddingRight: '20rpx',fontSize: valiFormData.agentGrade==''&&valiFormData.agentGrade!==0?'24rpx':'28rpx'}">{{valiFormData.agentGrade==''&&valiFormData.agentGrade!==0?'请选择代理商层级':agentGradeDict[agentIndex].text}}</view>
								<input hidden="true" type="text" v-model="valiFormData.agentGrade" />
								<view class="right-icon"></view>
							</view>
						</picker>
					</uni-forms-item>
					<uni-forms-item label="代理商地区" required name="district">
						<picker @change="districtChange" @columnchange="pickerColumnChange" mode="multiSelector" :value="multiIndex" :range="regionDict" range-key="name">
							<view style="display: flex;align-items: center;padding-right: 10rpx;">
								<view class="uni-input text-ellipsis_1" :style="{color: valiFormData.province?'':'#999999',paddingRight: '20rpx',fontSize: valiFormData.province?'28rpx':'24rpx',width: '57vw',textAlign: 'right'}">{{getSelectedArea()}}</view>
								<input hidden="true" type="text" v-model="valiFormData.district" />
								<view class="right-icon"></view>
							</view>
						</picker>
					</uni-forms-item>
					<uni-forms-item label="详细地址" required name="detailedAddress">
						<uni-easyinput type="text" v-model="valiFormData.detailedAddress" :clearable="false" :inputBorder="false" placeholder="请输入详细地址" />
					</uni-forms-item>
				</view>
			</uni-section>
			<uni-section titleFontSize="32rpx" title="法人信息" type="line">
				<view class="example" style="padding-bottom: 20rpx;">
					<uni-forms-item label="手机号" required name="phone">
						<uni-easyinput type="number" v-model="valiFormData.phone" :clearable="false" :inputBorder="false" placeholder="请输入手机号" />
					</uni-forms-item>
					<uni-forms-item label="电子邮箱" required name="email">
						<uni-easyinput type="text" v-model="valiFormData.email" :clearable="false" :inputBorder="false" placeholder="请输入电子邮箱" />
					</uni-forms-item>
					<uni-forms-item label="法人姓名" required name="legalName">
						<uni-easyinput type="text" v-model="valiFormData.legalName" :clearable="false" :inputBorder="false" placeholder="请输入法人姓名" />
					</uni-forms-item>
					<uni-forms-item label="身份证号" required name="idCard">
						<uni-easyinput type="idcard" v-model="valiFormData.idCard" :clearable="false" :inputBorder="false" placeholder="请输入身份证号" />
					</uni-forms-item>
					<view style="border-radius: 20rpx;background-color: #F5F5F5;">
						<view class="uni-forms-item" style="padding: 20rpx;display: flex;align-items: center;">
							<view class="uni-forms-item__label">
								<view><text class="is-required">*</text><text>头像面</text></view>
								<view style="color: #B2B2B2;font-size: 24rpx;margin-top: 20rpx;">上传您的身份证头像面</view>
							</view>
							<view class="grid-item-box">
								<img @click="picUP('frontUrl')" style="width: 260rpx;height: 168rpx;" :src="valiFormData.frontUrl?valiFormData.frontUrl:defaultImg" alt="" />
							</view>
						</view>
						<view class="uni-forms-item" style="padding: 20rpx;background-color: #F5F5F5;display: flex;align-items: center;margin-top: 20rpx;">
							<view class="uni-forms-item__label">
								<view><text class="is-required">*</text><text>国徽面</text></view>
								<view style="color: #B2B2B2;font-size: 24rpx;margin-top: 20rpx;">上传您的身份证国徽面</view>
							</view>
							<view class="grid-item-box">
								<img @click="picUP('contraryUrl')" style="width: 260rpx;height: 168rpx;" :src="valiFormData.contraryUrl?valiFormData.contraryUrl:defaultImg" alt="" />
							</view>
						</view>
					</view>
				</view>
			</uni-section>
			<uni-section titleFontSize="32rpx" title="上传凭证" type="line">
				<view class="example">
					<view class="uni-forms-item" style="padding-top: 20rpx;">
						<view class="uni-forms-item__label"><text class="is-required">*</text><text>合同凭证</text></view>
						<view style="margin-top: 30rpx;">
							<uni-grid :column="4" :show-border="false" :highlight="false">
								<uni-grid-item v-for="(item, index) in valiFormData.pactVoucher" :index="index" :key="index">
									<view class="grid-item-box" style="background-color: #fff;">
										<img style="width: 120rpx;height: 120rpx;" :src="item" alt="" />
										<img @click="delImages(item, 'pactVoucher')" class="del_round" src="/static/images/close_icon.png" alt="" />
									</view>
								</uni-grid-item>
								<uni-grid-item>
									<view class="grid-item-box">
										<img @click="picUP('pactVoucher')" style="width: 120rpx;height: 120rpx;" src="/static/images/img_ phone.png" alt="" />
									</view>
								</uni-grid-item>
							</uni-grid>
						</view>
					</view>
					<view class="uni-forms-item" style="border-top: 1px #eee solid;padding-top: 20rpx;">
						<view class="uni-forms-item__label"><text class="is-required">*</text><text>付款凭证</text></view>
						<view style="margin-top: 30rpx;">
							<uni-grid :column="4" :show-border="false" :highlight="false">
								<uni-grid-item v-for="(item, index) in valiFormData.payMentUrl" :index="index" :key="index">
									<view class="grid-item-box" style="background-color: #fff;">
										<img style="width: 120rpx;height: 120rpx;" :src="item" alt="" />
										<img @click="delImages(item,'payMentUrl')" class="del_round" src="/static/images/close_icon.png" alt="" />
									</view>
								</uni-grid-item>
								<uni-grid-item>
									<view class="grid-item-box">
										<img @click="picUP('payMentUrl')" style="width: 120rpx;height: 120rpx;" src="/static/images/img_ phone.png" alt="" />
									</view>
								</uni-grid-item>
							</uni-grid>
						</view>
					</view>
				</view>
			</uni-section>
			<view class="footer">
				<button type="primary" @click="submit('valiForm')">提交</button>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import { distributionApi, addressApi, userApi } from '@/api/index.js';
	export default {
		name: 'orderList',
		data() {
			return {
				// 校验表单数据
				valiFormData: {
					type: '',
					agentGrade: '',
					province: '', //省
					city: '', //市
					district: '', //区
					detailedAddress: '',
					phone: '',
					legalName: '',
					email: '',
					idCard: '',
					licenseUrl: [],
					frontUrl: '',
					contraryUrl: '',
					pactVoucher: [],
					payMentUrl: [],
				},
				defaultImg: '/static/images/img_just_data.png',
				// 单选数据源
				typeDict: [{
					text: '个人',
					value: '0'
				}, {
					text: '企业',
					value: '1'
				}],
				// 单选数据源
				agentGradeDict: [{
					text: '省代',
					value: 0
				}, {
					text: '市代',
					value: 1
				}, {
					text: '区县代',
					value: 2
				}],
				regionDict: [],
				region: [],
				// 校验规则
				rules: {
					type: {
						rules: [{
							required: true,
							errorMessage: '请选择代理商账户类型'
						}]
					},
					agentGrade: {
						rules: [{
							required: true,
							errorMessage: '请选择代理商层级'
						}]
					},
					detailedAddress: {
						rules: [{
							required: true,
							errorMessage: '请输入详细地址'
						}]
					},
					phone: {
						rules: [{
							required: true,
							errorMessage: '请输入手机号'
						}]
					},
					email: {
						rules: [{
							required: true,
							errorMessage: '请输入电子邮箱'
						}]
					},
					legalName: {
						rules: [{
							required: true,
							errorMessage: '请输入法人姓名'
						}]
					},
					idCard: {
						rules: [{
							required: true,
							errorMessage: '请输入身份证号'
						}]
					},
				},
				agentIndex: 0,
				multiIndex: [0, 0, 0]
			}
		},
		async onLoad(options) {
			await this.getRegions()
			if(options.params) {
				const params = JSON.parse(options.params)
				params.licenseUrl = params.licenseUrl?params.licenseUrl.split(','):[]
				params.pactVoucher = params.pactVoucher?params.pactVoucher.split(','):[]
				params.payMentUrl = params.payMentUrl?params.payMentUrl.split(','):[]
				this.valiFormData = {...params}
				console.log(this.valiFormData)
			}
			
		},
		methods: {
			async getRegions() {
				try {
					const res = await addressApi.getRegions();
					// 处理省市区数据
					if (res.data) {
						this.region = res.data
						this.regionDict = [this.region,this.region[0].posCityList,this.region[0].posCityList[0].posCountryList]
					}
				} catch (error) {
					console.error('获取失败:', error);
				}
			},
			onChange(e) {
				console.log(e.detail.value)
				this.valiFormData.type = e.detail.value
			},
			agentGradeChange: function(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.agentIndex = e.detail.value
				this.valiFormData.agentGrade = this.agentGradeDict[this.agentIndex].value
			},
			districtChange: function(e) {
				this.multiIndex = e.detail.value 
				this.valiFormData.province = this.regionDict[0][this.multiIndex[0]].code;
				this.valiFormData.city = this.regionDict[1][this.multiIndex[1]].code;
				this.valiFormData.district = this.regionDict[2][this.multiIndex[2]].code;
			},
			pickerColumnChange(e) {
			  const column = e.detail.column;
			  const value = e.detail.value;
			  this.multiIndex.splice(column, 1, value);
		
			  if (column === 0) {
				// 更新城市和区域数据
				const selectedProvince = this.region[value];
				this.regionDict[1] = selectedProvince.posCityList.map(city => city);
				this.regionDict[2] = selectedProvince.posCityList[0].posCountryList;
				this.multiIndex[1] = 0;
				this.multiIndex[2] = 0;
			  } else if (column === 1) {
				// 更新区域数据
				const selectedCity = this.region[this.multiIndex[0]].posCityList[value];
				this.regionDict[2] = selectedCity.posCountryList;
				this.multiIndex[2] = 0;
				console.log(selectedCity)
			  }
			},
			getSelectedArea() {
				if(this.valiFormData.id){
					const province = this.region.filter(item=>item.code==this.valiFormData.province)[0];
					const city = province.posCityList.filter(item=>item.code==this.valiFormData.city)[0];
					console.log(city)
					const area = city.posCountryList.filter(item=>item.code==this.valiFormData.district);
					 return `${province.name}/${city.name}/${area[0].name}`;
				}else if(!this.valiFormData.province) {
					return '请选择代理商地区'
				}else{
				  const province = this.regionDict[0][this.multiIndex[0]].name;
				  const city = this.regionDict[1][this.multiIndex[1]].name;
				  const area = this.regionDict[2][this.multiIndex[2]].name;
				  return `${province}/${city}/${area}`;
				}
			},
			// 获取表单数据
			async submit(ref) {
				this.$refs[ref].validate().then(res => {
					console.log('success', res);
					res.licenseUrl = this.valiFormData.licenseUrl?this.valiFormData.licenseUrl.join(','):''
					res.pactVoucher = this.valiFormData.pactVoucher?this.valiFormData.pactVoucher.join(','):''
					res.payMentUrl = this.valiFormData.payMentUrl?this.valiFormData.payMentUrl.join(','):''
					res.frontUrl = this.valiFormData.frontUrl
					res.contraryUrl = this.valiFormData.contraryUrl
					res.province = this.valiFormData.province;
					res.city = this.valiFormData.city;
					res.id = this.valiFormData.id?this.valiFormData.id:'';
					if(this.valiFormData.type==1&&!res.licenseUrl) {
						uni.showToast({
							title: '请上传主体信息',
							icon: 'none'
						})
						return
					}
					if(!res.frontUrl) {
						uni.showToast({
							title: '请上传头像面',
							icon: 'none'
						})
						return
					}
					if(!res.contraryUrl) {
						uni.showToast({
							title: '请上传国徽面',
							icon: 'none'
						})
						return
					}
					if(!res.pactVoucher) {
						uni.showToast({
							title: '请上传合同凭证',
							icon: 'none'
						})
						return
					}
					if(!res.payMentUrl) {
						uni.showToast({
							title: '请上传付款凭证',
							icon: 'none'
						})
						return
					}
					this.addAgent(res)
				}).catch(err => {
					console.log('err', err);
				})
			},
			// 新增表单
			addAgent(params) {
				distributionApi[this.valiFormData.id?'editAgent':'addAgent']({...params}).then((res) => {
					console.log(res)
					if (res.code !== 0) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					} else {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						})
						setTimeout(() => {
							// 进列表
							uni.navigateBack()
						}, 1000)
					}
				})
			},
			// 上传图片
			picUP: function(name) {
				var that = this
				uni.chooseImage({
					count: 1, //图片可选择数量
					sizeType: ['compressed'], //original 原图，compressed 压缩图，默认二者都有
					sourceType: ['album', 'camera'], //album 从相册选图，camera 使用相机，默认二者都有。
					extension: ['.png', '.jpg'], // 限制可选择的图片格式
					success: res => {
						that.uploadImg(res.tempFilePaths[0],name);
					},
					fail: res => {
					},
				});
			},
			async uploadImg(file,name) {
				try {
					const res = await userApi.uploadFile(file);
					if (res.data) {
						uni.showToast({
							title: '上传成功！'
						});
						if(name=='frontUrl'||name=='contraryUrl'){
							this.valiFormData[name] = res.data.url;
						}else{
							this.valiFormData[name].push(res.data.url)
						}
					}
				} catch (error) {
					console.error('获取失败:', error);
				}
			},
			delImages(item,name) {
				this.valiFormData[name] = this.valiFormData[name].filter(el=>el!==item)
			}
		}
	}
</script>


<style lang="scss">
	page{
		background-color: #F7F7F7;
	}
</style>
<style lang="scss" scoped>
	.page{
		padding: 30rpx;
		padding-bottom: 200rpx;
		.example{
			border-radius: 20rpx;
			background-color: white;
			padding: 0 24rpx;
		}
	}
	.audit_top{
		padding: 20rpx 24rpx;
		border-radius: 20rpx;
		background-image: linear-gradient(to right, #E8E8F6, #DFF2F6);
	}
	/deep/.uni-section{
		background-color: #F7F7F7;
	}
	/deep/.uni-section-header{
		padding: 24rpx 0 !important;
	}
	/deep/ .uni-section-header__decoration line{
		background-color: #7675FE;
	}
	/deep/ .uni-section .uni-section-header__content .distraction {
		font-weight: 600;
	}
	/deep/.uni-forms-item {
		justify-content: space-between;
		align-items: center;
	}
	/deep/.uni-forms-item__content {
		flex: none;
	}
	/deep/.uni-easyinput__content-input {
		text-align: right;
	}
	/deep/.uni-forms-item__label{
		width: auto !important;
		font-size: 28rpx !important;
		color: #333333;
		// font-weight: 600;
	}
	.uni-forms-item__label{
		font-size: 28rpx;
		color: #333333;
	}
	.uni-forms-item .is-required {
	    color: #dd524d;
	    font-weight: bold;
	}
	.grid-item-box{
		position: relative;
		display: flex;
	}
	.del_round{
		width: 30rpx;
		height: 30rpx;
		position: absolute;
		right: 42rpx;
		top: 0;
		z-index: 5;
		background-color: #FFFFFF;
		border-radius: 50%;
	}
	.footer{
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 12rpx 30rpx;
		padding-bottom: 68rpx;
		z-index: 5;
		background-color: #F7F7F7;
		button{
			color: white;
			font-size: 32rpx;
			padding: 22rpx 0;
			background-color: #7675FE;
		}
	}
</style>