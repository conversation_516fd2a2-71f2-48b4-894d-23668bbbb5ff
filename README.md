# 分销小程序

基于HBuilder X开发的微信小程序分销系统，包含完整的用户管理、商品分销、收益统计、学习中心等功能模块。

## 项目结构

```
├── api/                    # API接口管理
│   └── index.js           # 统一的API接口定义
├── mixins/                # 全局混入
│   └── global.js          # 全局混入方法和属性
├── pages/                 # 页面文件
│   ├── index/             # 首页
│   ├── earnings/          # 收益页面
│   ├── college/           # 商学院页面
│   └── my/                # 我的页面
├── static/                # 静态资源
│   └── images/            # 图片资源
├── utils/                 # 工具类
│   ├── request.js         # 网络请求封装
│   ├── storage.js         # 本地存储封装
│   └── common.js          # 常用工具方法
├── App.vue                # 应用入口
├── main.js                # 主入口文件
├── pages.json             # 页面配置
├── manifest.json          # 应用配置
└── uni.scss               # 全局样式
```

## 功能特性

### 🏠 首页
- 轮播图展示
- 快捷功能菜单
- 今日数据统计
- 热门产品推荐

### 💰 收益中心
- 今日收益展示
- 累计收益统计
- 代理商/团队/客户数据
- 收益趋势分析

### 🎓 商学院
- 课程分类浏览
- 推荐课程展示
- 学习进度跟踪
- 知识技能提升

### 👤 个人中心
- 用户信息展示
- 余额管理
- 提现功能
- 各类管理功能

## 技术栈

- **框架**: uni-app
- **开发工具**: HBuilder X
- **UI**: 原生CSS + 渐变设计
- **状态管理**: 本地存储 + 全局混入
- **网络请求**: 封装的request工具
- **平台**: 微信小程序

## 核心封装

### 1. 网络请求 (utils/request.js)
```javascript
import { get, post, put, del } from '@/utils/request.js';

// 使用示例
get('/api/user/info').then(res => {
  console.log(res);
});

post('/api/user/login', { username, password }).then(res => {
  console.log(res);
});
```

### 2. 本地存储 (utils/storage.js)
```javascript
import { setStorage, getStorage, userStorage } from '@/utils/storage.js';

// 基础存储
setStorage('key', 'value', 7 * 24 * 60 * 60 * 1000); // 7天过期
const value = getStorage('key', 'defaultValue');

// 用户相关存储
userStorage.setToken('token');
userStorage.setUserInfo(userInfo);
```

### 3. 常用工具 (utils/common.js)
```javascript
import { formatMoney, formatDate, showToast } from '@/utils/common.js';

// 格式化金额
const money = formatMoney(1234.56); // "1,234.56"

// 格式化日期
const date = formatDate(new Date(), 'YYYY-MM-DD'); // "2024-01-01"

// 显示提示
showToast('操作成功', 'success');
```

### 4. 全局混入 (mixins/global.js)
在所有页面中可直接使用的方法：
```javascript
// 页面中直接使用
this.$formatMoney(1234.56);
this.$showToast('提示信息');
this.$navigateTo('/pages/detail/detail', { id: 123 });
this.requireLogin(() => {
  // 需要登录才能执行的操作
});
```

## 开发指南

### 1. 环境准备
- 安装HBuilder X
- 配置微信开发者工具
- 申请微信小程序AppID

### 2. 项目配置
1. 修改 `manifest.json` 中的AppID
2. 配置 `utils/request.js` 中的API基础地址
3. 根据需要调整 `pages.json` 中的页面配置

### 3. API接口配置
在 `api/index.js` 中配置所有API接口：
```javascript
export const userApi = {
  login(data) {
    return post('/user/login', data);
  },
  getUserInfo() {
    return get('/user/info');
  }
};
```

### 4. 页面开发
使用全局混入的方法：
```vue
<template>
  <view>
    <text>{{ $formatMoney(amount) }}</text>
    <button @click="handleLogin">登录</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      amount: 1234.56
    };
  },
  methods: {
    handleLogin() {
      this.requireLogin(() => {
        // 登录后的操作
        this.$showToast('登录成功');
      });
    }
  }
};
</script>
```

## 部署说明

### 1. 开发环境
```bash
# 在HBuilder X中运行到微信开发者工具
# 或直接在HBuilder X中预览
```

### 2. 生产环境
1. 在HBuilder X中选择"发行" -> "小程序-微信"
2. 生成的代码上传到微信公众平台
3. 提交审核并发布

## 注意事项

1. **图片资源**: 项目中的图片路径为占位符，请替换为实际的图片资源
2. **API地址**: 请在 `utils/request.js` 中配置正确的API服务器地址
3. **AppID配置**: 请在 `manifest.json` 中配置正确的微信小程序AppID
4. **权限配置**: 根据功能需要在 `manifest.json` 中配置相应的权限

## 扩展功能

项目已预留扩展接口，可根据业务需求添加：
- 商品管理模块
- 订单管理系统
- 支付功能集成
- 消息推送功能
- 数据统计分析

## 技术支持

如有问题，请检查：
1. HBuilder X版本是否为最新
2. 微信开发者工具版本是否兼容
3. API接口是否正确配置
4. 网络请求是否正常

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完成基础框架搭建
- 实现四个主要页面
- 完成工具类封装
