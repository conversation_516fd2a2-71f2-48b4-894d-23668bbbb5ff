<template>
	<view class="order-list-page">
		<!-- 订单状态 -->
		<view class="order-list-header">
			<view class="one-status" v-for="(item, index) in orderTypes" :key="index"
				:class="index == tabIndex ? 'active' : ''" @click="onClickItem(item, index)">
				{{ item.name }}
				<image v-if="index == tabIndex" class="select-line" src="/static/images/icon_option_select.png" mode="aspectFit"></image>
				</view>
		</view>
		<view class="search_list" style="display: flex;align-items: center;">
			<uni-search-bar style="flex: 1;" v-model="keyWords" bgColor="#FFFFFF" radius="5" placeholder="请输入团队名称" clearButton="auto" cancelButton="none" @confirm="search" @blur="onBlur" />
			<button class="search_btn" @click="onSearch">搜索</button>
		</view>
		<view style="padding: 0 24rpx;color: #999999;">
			<view>团队共计：{{totalCount}}</view>
			<view style="margin-top: 4rpx;">待审核：{{countPending}}; 审核成功：{{countPass}}; 审核失败：{{countClose}}</view>
		</view>
		<!-- 订单列表 -->
		<view class="order-list">
			<view class="each-order" v-for="(item, index) of orderList" :key="index" @click="edit(item)">
				<view style="flex: 1;margin-right: 20rpx;">
					<view style="display: flex;justify-content: space-between;">
						<view style="display: flex;">
							<view>{{item.name}}</view>
							<!-- <view v-if="item.ifJob=='1'" class="tag_name">在职</view> -->
						</view>
						<view :class="'state_tag state_tag'+item.auditStatus">{{item.auditStatus | formatState}}</view>
					</view>
					<view style="font-size: 24rpx;color: #999999;">
						<view style="display: flex;justify-content: space-between;margin-top: 14rpx;">
							<view>{{item.phone}}</view>
							<view>{{item.createDate}}</view>
						</view>
					</view>
				</view>
				<view v-if="item.auditStatus==3" class="right-icon"></view>
			</view>
		</view>
		<uni-load-more iconType="circle" :status="loadStatus" />
	</view>
</template>

<script>
	import { distributionApi } from '@/api/index.js';
	export default {
		name: 'orderList',
		data() {
			return {
				totalCount: 0,
				countPending: 0,
				countPass: 0,
				countClose: 0,
				orderTypes: [{
						status: 0,
						name: '全部'
					},
					{
						status: 1,
						name: '待审核'
					},
					{
						status: 2,
						name: '审核成功'
					},
					{
						status: 3,
						name: '审核失败'
					}
				],
				tabIndex: '0',
				orderParams: {
					orderStatus: 0,
					page: 1,
					limit: 10
				},
				loadStatus: 'loading', // more,loading,noMore
				activeColor: '#007aff',
				orderList: [],
				isLoadAll: false,
				keyWords: ''
			}
		},
		filters: {
			formatState: function(_state) {
				if (_state === '0') {
					return '全部'
				} else if (_state === '1') {
					return '待审核'
				} else if (_state === '2') {
					return '审核成功'
				} else if (_state === '3') {
					return '审核失败'
				}
			}
		},
		onPullDownRefresh() {
			this.isLoadAll = false
			this.orderParams.page = 1
			this.getOrderData()
		},
		onReachBottom () {
			console.log('加载中')
			if (!this.isLoadAll) {
				this.orderParams.page++
				this.getOrderData()
			}
		},
		onShow: function() {
			this.orderList=[]
			this.orderParams.page = 1
			this.getOrderData()
		},
		methods: {
			async search(val) {
				this.orderList=[]
				this.orderParams.page = 1
				this.keyWords = val.value
				this.getOrderData()
				console.log(val)
			},
			async onBlur(val) {
				this.keyWords = val.value
				console.log(val)
			},
			async onSearch() {
				this.orderList=[]
				this.orderParams.page = 1
				this.getOrderData()
			},
			// 获取列表
			async getOrderData() {
				let that = this
				that.loadStatus = 'loading'
				await distributionApi.getMerchantList({
					  "name": that.keyWords,
					  "limit": that.orderParams.limit,
					  "page": that.orderParams.page,
					  auditStatus: that.orderParams.orderStatus
				}).then((res) => {
					console.log(res)
					if (res.code !== 0) {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})
						that.loadStatus = 'no-more'
					} else {
						that.totalCount = res.data.totalCount
						that.countPending = res.data.countPending
						that.countPass = res.data.countPass
						that.countClose = res.data.countClose
						that.orderList = that.orderList.concat(res.data.page.records)
						that.isLoadAll = that.orderParams.page >= res.data.page.pages //4
						that.loadStatus = 'no-more'
						console.log(that.orderList)
					}
				}).catch(()=>{
					that.loadStatus = 'no-more'
				})
			},
			// 切换状态tab
			onClickItem(item, inx) {
				this.tabIndex = inx
				this.orderParams.orderStatus = item.status
				this.orderParams.page = 1
				this.orderList = []
				this.getOrderData()
			},
			edit(item) {
				if(item.auditStatus!=='3') return
				uni.navigateTo({
					url: '/subpages/merchant/addMerchant?params='+JSON.stringify(item)
				})
			}
		}
	}
</script>


<style lang="scss">
	page{
		background-color: #F7F7F7;
	}
	.order-list-page {
		height: 100%;

		.type-tabs {
			.segmented-control__text {
				font-size: 24rpx;
			}
		}
	}
</style>
<style lang="scss" scoped>
	.order-list-page {
		.order-list-header {
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			.one-status {
				padding: 10rpx 0;
				font-size: 28rpx;
				position: relative;
				color: #9FA3B0;
				position: relative;

				&.active {
					color: #171B25;
					font-weight: 600;
				}
				image{
					position: absolute;
					bottom: -16rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 155rpx;
					height: 30rpx;
					pointer-events: none; /* 防止图片阻挡点击事件 */
				}
			}
		}
		.search_list{
			background-color: #FFFFFF;
			margin: 24rpx;
			padding: 10rpx 20rpx 10rpx 0;
			border-radius: 10rpx;
			.search_btn{
				font-size: 24rpx;
				color: #FFFFFF;
				background-color: #4D40E5;
				padding: 12rpx 30rpx;
			}
		}
		/deep/ .uni-searchbar{
			padding: 0 10rpx;
		}
		.order-list {
			margin: 24rpx;
			background-color: #FFFFFF;
			border-radius: 20rpx;
			padding-left: 30rpx;
			.each-order{
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 28rpx;
				padding: 30rpx 30rpx 30rpx 0;
				border-bottom: 1px solid #F5F5F5;
				.tag_name{
					color: white;
					font-size: 22rpx;
					border-radius: 30rpx;
					padding: 4rpx 14rpx;
					background-color: #4D59DD;
					margin-left: 10rpx;
				}
				.state_tag{
					font-size: 24rpx;
					&.state_tag1{
						color: #333333;
					}
					&.state_tag2{
						color: #00BA5F;
					}
					&.state_tag3{
						color: #F23D3D;
					}
				}
			}
		}
	}
</style>