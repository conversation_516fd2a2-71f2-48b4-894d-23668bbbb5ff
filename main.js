import App from './App'

// 导入全局混入
import globalMixin from './mixins/global.js'

// 导入API
import api from './api/index.js'

// 导入工具类
import utils from './utils/common.js'
import storage from './utils/storage.js'

// 导入Vuex store
import store from './store/index.js'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

Vue.config.productionTip = false

// 注册全局混入
Vue.mixin(globalMixin)

// 挂载到Vue原型上
Vue.prototype.$api = api
Vue.prototype.$utils = utils
Vue.prototype.$storage = storage

App.mpType = 'app'
const app = new Vue({
  store,
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)

  // 注册Vuex store
  app.use(store)

  // 注册全局混入
  app.mixin(globalMixin)

  // 全局属性
  app.config.globalProperties.$api = api
  app.config.globalProperties.$utils = utils
  app.config.globalProperties.$storage = storage

  return {
    app
  }
}
// #endif