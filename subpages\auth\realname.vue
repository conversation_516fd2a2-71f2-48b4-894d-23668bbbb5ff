<template>
	<view class="page">
		<!-- 顶部实名认证模块 -->
		<view class="header-section">
			<image class="header-bg" src="/static/images/img_autonym_setup.png" mode="aspectFill"></image>
		</view>

		<!-- 身份证上传模块 -->
		<view class="upload-section">
			<!-- 头像面 -->
			<view class="upload-item">
				<view class="upload-content">
					<view class="upload-left">
						<view class="upload-label">
							<text class="required">*</text>
							<text class="label-text">头像面</text>
						</view>
						<view class="upload-desc">上传您的身份证正面照</view>
					</view>
					<view class="upload-card" :class="{ 'disabled': isAuthenticated }" @click="!isAuthenticated && uploadFrontImage()">
						<image class="upload-image" :src="formData.frontImage || '/static/images/img_ just_ data.png'" mode="aspectFill"></image>
					</view>
				</view>
			</view>

			<!-- 国旗面 -->
			<view class="upload-item">
				<view class="upload-content">
					<view class="upload-left">
						<view class="upload-label">
							<text class="required">*</text>
							<text class="label-text">国旗面</text>
						</view>
						<view class="upload-desc">上传您的身份证反面照</view>
					</view>
					<view class="upload-card" :class="{ 'disabled': isAuthenticated }" @click="!isAuthenticated && uploadBackImage()">
						<image class="upload-image" :src="formData.backImage || '/static/images/img_ against_ data.png'" mode="aspectFill"></image>
					</view>
				</view>
			</view>
		</view>

		<!-- 信息填写模块 -->
		<view class="form-section">
			<view class="form-item">
				<view class="form-label">姓名</view>
				<input
					class="form-input"
					:class="{ 'readonly': isAuthenticated }"
					type="text"
					placeholder="请输入姓名"
					v-model="formData.name"
					:disabled="isAuthenticated"
				/>
			</view>
			<view class="form-item">
				<view class="form-label">身份证号</view>
				<input
					class="form-input"
					:class="{ 'readonly': isAuthenticated }"
					type="text"
					placeholder="请输入本人身份证号"
					v-model="formData.idCard"
					maxlength="18"
					:disabled="isAuthenticated"
				/>
			</view>
		</view>

		<!-- 确定按钮 - 只在未认证时显示 -->
		<view class="submit-section" v-if="!isAuthenticated">
			<button class="submit-btn" @click="handleSubmit">确定</button>
		</view>
	</view>
</template>

<script>
	import { getRealNameInfo, submitRealName } from '@/api/user.js';
	import { userApi } from '@/api/index.js';

	export default {
		data() {
			return {
				formData: {
					name: '',
					idCard: '',
					frontImage: '',
					backImage: ''
				},
				realNameInfo: null, // 实名认证详情数据
				isAuthenticated: false // 是否已实名认证
			}
		},

		onLoad() {
			this.loadRealNameInfo();
		},
		methods: {
			// 加载实名认证详情
			async loadRealNameInfo() {
				try {
					uni.showLoading({
						title: '加载中...',
						mask: true
					});

					const res = await getRealNameInfo();

					uni.hideLoading();

					if (res.code === 0) {
						this.realNameInfo = res.data;

						// 如果data不为空，说明已经实名认证，回显数据
						if (res.data) {
							this.isAuthenticated = true; // 设置已认证状态
							this.formData.name = res.data.realName || '';
							this.formData.idCard = res.data.idCard || '';
							this.formData.frontImage = res.data.positiveImg || '';
							this.formData.backImage = res.data.backImg || '';

							console.log('已实名认证，回显数据:', res.data);
						} else {
							// data为空，需要填写实名认证信息
							this.isAuthenticated = false; // 设置未认证状态
							console.log('未实名认证，需要填写信息');
						}
					}
				} catch (error) {
					uni.hideLoading();
					console.error('获取实名认证详情失败:', error);
					uni.showToast({
						title: '获取信息失败',
						icon: 'none'
					});
				}
			},

			// 上传身份证正面
			uploadFrontImage() {
				uni.showActionSheet({
					itemList: ['拍照', '从相册选择'],
					success: (res) => {
						const sourceType = res.tapIndex === 0 ? ['camera'] : ['album'];
						this.chooseImage(sourceType, 'front');
					}
				});
			},

			// 上传身份证反面
			uploadBackImage() {
				uni.showActionSheet({
					itemList: ['拍照', '从相册选择'],
					success: (res) => {
						const sourceType = res.tapIndex === 0 ? ['camera'] : ['album'];
						this.chooseImage(sourceType, 'back');
					}
				});
			},

			// 选择图片
			chooseImage(sourceType, type) {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: sourceType,
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						// 先显示本地图片
						if (type === 'front') {
							this.formData.frontImage = tempFilePath;
						} else {
							this.formData.backImage = tempFilePath;
						}
						// 上传图片到服务器
						this.uploadImage(tempFilePath, type);
					},
					fail: () => {
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},

			// 上传图片到服务器
			async uploadImage(filePath, type) {
				try {
					uni.showLoading({
						title: '上传中...'
					});

					// 调用上传文件接口
					const uploadRes = await userApi.uploadFile(filePath);
					console.log('上传文件响应:', uploadRes);

					if (uploadRes.data && uploadRes.data.url) {
						// 更新对应的图片URL
						if (type === 'front') {
							this.formData.frontImage = uploadRes.data.url;
						} else {
							this.formData.backImage = uploadRes.data.url;
						}

						uni.hideLoading();
						uni.showToast({
							title: '图片上传成功',
							icon: 'success'
						});
					} else {
						throw new Error('上传失败，未获取到图片地址');
					}
				} catch (error) {
					uni.hideLoading();
					console.error('上传图片失败:', error);

					// 恢复原来的图片
					if (type === 'front') {
						this.formData.frontImage = '';
					} else {
						this.formData.backImage = '';
					}

					uni.showToast({
						title: error.msg || '图片上传失败',
						icon: 'none'
					});
				}
			},

			// 提交表单
			async handleSubmit() {
				// 验证表单
				if (!this.formData.name) {
					uni.showToast({
						title: '请输入姓名',
						icon: 'none'
					});
					return;
				}

				if (!this.formData.idCard) {
					uni.showToast({
						title: '请输入身份证号',
						icon: 'none'
					});
					return;
				}

				// 验证身份证号格式
				const idCardReg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if (!idCardReg.test(this.formData.idCard)) {
					uni.showToast({
						title: '请输入正确的身份证号',
						icon: 'none'
					});
					return;
				}

				// 验证图片是否上传
				if (!this.formData.frontImage) {
					uni.showToast({
						title: '请上传身份证正面照',
						icon: 'none'
					});
					return;
				}

				if (!this.formData.backImage) {
					uni.showToast({
						title: '请上传身份证反面照',
						icon: 'none'
					});
					return;
				}

				// 构造提交数据
				const submitData = {
					realName: this.formData.name,
					idCard: this.formData.idCard,
					positiveImg: this.formData.frontImage, // 正面照
					backImg: this.formData.backImage // 反面照
				};

				try {
					uni.showLoading({
						title: '提交中...',
						mask: true
					});

					const res = await submitRealName(submitData);

					uni.hideLoading();

					if (res.code === 0) {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						});

						// 返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				} catch (error) {
					uni.hideLoading();
					console.error('提交实名认证失败:', error);
					uni.showToast({
						title: error.msg || '提交失败，请重试',
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style scoped>
	.page {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	/* 顶部实名认证模块 */
	.header-section {
		position: relative;
		width: 100%;
		height: 320rpx;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.header-bg {
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
		overflow: hidden;
	}

	/* 身份证上传模块 */
	.upload-section {
		margin: 0 30rpx 30rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
	}

	.upload-item {
		margin-bottom: 60rpx;
	}

	.upload-item:last-child {
		margin-bottom: 0;
	}

	.upload-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.upload-left {
		flex: 1;
		margin-right: 30rpx;
	}

	.upload-label {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.required {
		color: #ff4757;
		font-size: 28rpx;
		margin-right: 8rpx;
	}

	.label-text {
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
	}

	.upload-desc {
		font-size: 24rpx;
		color: #999999;
	}

	.upload-card {
		width: 200rpx;
		height: 140rpx;
		border-radius: 16rpx;
		overflow: hidden;
		background-color: #f8f9fa;
		flex-shrink: 0;
	}

	.upload-image {
		width: 100%;
		height: 100%;
	}

	/* 信息填写模块 */
	.form-section {
		margin: 0 30rpx 60rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.form-item {
		display: flex;
		align-items: center;
		padding: 40rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.form-label {
		width: 160rpx;
		font-size: 28rpx;
		color: #333333;
		flex-shrink: 0;
	}

	.form-input {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
		text-align: right;
	}

	.form-input::placeholder {
		color: #cccccc;
	}

	/* 只读状态样式 */
	.form-input.readonly {
		// background-color: #f5f5f5;
		color: #999999;
	}

	/* 禁用状态的上传卡片 */
	.upload-card.disabled {
		opacity: 0.6;
		pointer-events: none;
	}

	/* 确定按钮 */
	.submit-section {
		padding: 0 30rpx 60rpx;
	}

	.submit-btn {
		width: 100%;
		height: 88rpx;
		background-color: #4D40E5;
		border-radius: 20rpx;
		border: none;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.submit-btn:active {
		opacity: 0.8;
	}
</style>
