/**
 * API接口管理
 */

import { get, post, put, del, upload } from '@/utils/request.js';

// 用户相关接口
export const userApi = {
	// 用户登录
	login(data) {
		return post('/app/auth/login', data);
	},
	
	// 获取用户信息
	getUserInfo() {
		return post('/app/auth/getUserInfo',{},{showLoading: false});
	},

	// 获取我的账户信息
	getMyAccount() {
		return post('/app/account/mine');
	},

	// 上传文件
	uploadFile(filePath) {
		return upload('/app/file/upload', filePath, 'test');
	},

	// 修改用户头像
	updateUserPhoto(img) {
		return post('/app/auth/userPhoto', { img });
	},

	// 发送短信验证码
	sendSmsCode(phone, deviceID, imgCode) {
		return post('/app/auth/getSms', {
			phone: phone,
			tenantId: 3,
			deviceID: deviceID,
			imgCode: imgCode
		});
	},

	// 重置密码
	resetPassword(data) {
		return post('/app/auth/resetPassword', data);
	},
	
	// 更新用户信息
	updateUserInfo(data) {
		return put('/user/info', data);
	},
	
	// 用户注册
	register(data) {
		return post('/user/register', data);
	},
		
	// 修改密码
	changePassword(data) {
		return post('/user/change-password', data);
	},
	
	// 实名认证
	realNameAuth(data) {
		return post('/user/real-name-auth', data);
	},
	
	// 上传头像
	uploadAvatar(filePath) {
		return upload('/user/upload-avatar', filePath);
	}
};

// 分销相关接口
export const distributionApi = {
	// 获取分销统计数据
	getStats() {
		return get('/distribution/stats');
	},
	
	// 获取收益明细
	getEarnings(params) {
		return get('/distribution/earnings', params);
	},
	// 新增代理商
	addAgent(params) {
		return post('/app/index/agent/add', params);
	},
	// 编辑代理商
	editAgent(params) {
		return post('/app/index/agent/edit', params);
	},
	// 代理商列表
	getAgentList(params) {
		return post('/app/index/agentList', params);
	},
	// 新增商户
	addMerchant(params) {
		return post('/app/index/merchant/add', params);
	},
	// 编辑商户
	editMerchant(params) {
		return post('/app/index/merchant/edit', params);
	},
	// 获取商户列表
	getMerchantList(params) {
		return post('/app/index/merList', params);
	},
	// 获取商户分类
	getMerchantCategory() {
		return post('/app/index/merchant/category');
	},
	// 获取商户类型
	getMerchantType() {
		return post('/app/index/merchant/type');
	},
	// 新增团队
	addTeam(params) {
		return post('/app/index/team/add', params);
	},
	// 编辑团队
	editTeam(params) {
		return post('/app/index/team/edit', params);
	},
	// 团队列表
	getTeamList(params) {
		return post('/app/index/teamList', params);
	},
	// 获取客户列表
	getCustomerList(params) {
		return get('/distribution/customers', params);
	},
	
	// 邀请好友
	inviteFriend(data) {
		return post('/distribution/invite', data);
	},
	
	// 获取邀请码
	getInviteCode() {
		return get('/distribution/invite-code');
	},
	
	// 申请提现
	applyWithdraw(data) {
		return post('/distribution/withdraw', data);
	},
	
	// 获取提现记录
	getWithdrawList(params) {
		return get('/distribution/withdraw-list', params);
	}
};

// 商品相关接口
export const productApi = {
	// 获取商品列表
	getProductList(params) {
		return post('/app/goods/list/0', params,{showLoading: false});
	},
	
	// 获取商品详情
	getProductDetail(id) {
		return get(`/product/detail/${id}`);
	},
	
	// 获取商品分类
	getCategories() {
		return get('/product/categories');
	},
	
	// 搜索商品
	searchProducts(params) {
		return get('/product/search', params);
	},
	
	// 获取推荐商品
	getRecommendProducts(params) {
		return get('/product/recommend', params);
	},
	
	// 获取热门商品
	getHotProducts(params) {
		return get('/product/hot', params);
	},
	
	// 收藏商品
	favoriteProduct(productId) {
		return post('/product/favorite', { productId });
	},
	
	// 取消收藏
	unfavoriteProduct(productId) {
		return del('/product/favorite', { productId });
	},
	
	// 获取收藏列表
	getFavoriteList(params) {
		return get('/product/favorite-list', params);
	}
};



// 地址相关接口
export const addressApi = {
	// 获取地址列表
	getAddressList() {
		return post('/app/address/list');
	},

	// 根据ID查询地址详情
	getAddressById(id) {
		return post('/app/address/searchById?addressId=' + id);
	},

	// 添加地址
	addAddress(data) {
		return post('/app/address/add', data);
	},
	
	// 更新地址
	updateAddress(data) {
		return post('/app/address/update', data);
	},
	
	// 删除地址
	deleteAddress(id) {
		return post('/app/address/delete?id=' + id, {});
	},
	
	// 设置默认地址
	setDefaultAddress(id) {
		return post('/app/address/setDefault', { id });
	},
	
	// 获取省市区数据
	getRegions(data = {}) {
		return post('/app/address/prov', data);
	}
};

// 课程相关接口
export const courseApi = {
	// 获取课程列表
	getCourseList(params) {
		return get('/course/list', params);
	},

	// 获取课程详情
	getCourseDetail(id) {
		return get(`/course/detail/${id}`);
	},
	
	// 获取课程分类
	getCourseCategories() {
		return get('/course/categories');
	},
	
	// 购买课程
	buyCourse(data) {
		return post('/course/buy', data);
	},
	
	// 获取我的课程
	getMyCourses(params) {
		return get('/course/my-courses', params);
	},
	
	// 获取学习进度
	getStudyProgress(courseId) {
		return get(`/course/progress/${courseId}`);
	},
	
	// 更新学习进度
	updateStudyProgress(data) {
		return post('/course/update-progress', data);
	}
};

// 系统相关接口
export const systemApi = {
	// 首页
	getIndex() {
		return post('/app/index',{},{showLoading: true});
	},
	// 获取轮播图
	getBanners(position = 'home') {
		return get('/system/banners', { position });
	},
	
	// 获取公告列表
	getNotices(params) {
		return post('/app/notice/list', params, {showLoading: false});
	},
	
	// 获取公告详情
	getNoticeDetail(id) {
		return get(`/system/notice/${id}`);
	},
	
	// 获取系统配置
	getConfig() {
		return get('/system/config');
	},
	
	// 意见反馈
	feedback(data) {
		return post('/system/feedback', data);
	},
	
	// 获取版本信息
	getVersion() {
		return get('/system/version');
	},
	
	// 检查更新
	checkUpdate() {
		return get('/system/check-update');
	}
};

// 统计相关接口
export const statisticsApi = {
	// 获取首页统计数据
	getHomeStats() {
		return get('/statistics/home');
	},
	
	// 获取收益统计
	getEarningsStats(params) {
		return get('/statistics/earnings', params);
	},
	
	// 获取订单统计
	getOrderStats(params) {
		return get('/statistics/orders', params);
	},
	
	// 获取客户统计
	getCustomerStats(params) {
		return get('/statistics/customers', params);
	}
};

export default {
	userApi,
	distributionApi,
	productApi,
	orderApi,
	addressApi,
	courseApi,
	systemApi,
	statisticsApi
};
