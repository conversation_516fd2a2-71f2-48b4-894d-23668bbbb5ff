<template>
	<view class="settings-page">
		<!-- 设置内容 -->
		<view class="settings-content">
			<!-- 设置项列表 -->
			<view class="settings-section">
				<view class="setting-item" @click="handlePasswordChange">
					<view class="setting-left">
						<text class="setting-title">修改密码</text>
					</view>
					<view class="setting-right">
						<image class="arrow-icon" src="/static/images/icon_more_10_666_1.5.png" mode="aspectFill"></image>
					</view>
				</view>

				<view class="setting-item" @click="handleAgreement">
					<view class="setting-left">
						<text class="setting-title">协议规则</text>
					</view>
					<view class="setting-right">
						<image class="arrow-icon" src="/static/images/icon_more_10_666_1.5.png" mode="aspectFill"></image>
					</view>
				</view>
			</view>
		</view>

		<!-- 退出登录按钮 -->
		<view class="logout-section">
			<view class="logout-btn" @click="handleLogout">
				<text class="logout-text">退出登录</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0
			}
		},
		onLoad() {
			this.getSystemInfo();
		},
		methods: {
			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 修改密码
			handlePasswordChange() {
				console.log('修改密码');
				// 跳转到修改密码页面
				uni.navigateTo({
					url: '/subpages/auth/forgot-password'
				});
			},

			// 协议规则
			handleAgreement() {
				console.log('协议规则');
				// 可以跳转到协议页面或显示协议内容
				uni.showToast({
					title: '协议规则功能待开发',
					icon: 'none'
				});
			},

			// 退出登录
			handleLogout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除登录状态
							uni.removeStorageSync('token');
							uni.removeStorageSync('userInfo');
							
							// 跳转到登录页面
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					}
				});
			}
		}
	}
</script>

<style scoped>
	.settings-page {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	/* 设置内容样式 */
	.settings-content {
		padding-top: 24rpx;
	}

	.settings-section {
		background-color: #ffffff;
		margin: 0 32rpx;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.setting-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 40rpx 32rpx;
		border-bottom: 1rpx solid #f0f0f0;
		min-height: 120rpx;
		box-sizing: border-box;
	}

	.setting-item:last-child {
		border-bottom: none;
	}

	.setting-left {
		flex: 1;
	}

	.setting-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: 400;
	}

	.setting-right {
		display: flex;
		align-items: center;
	}

	.arrow-icon {
		width: 20rpx;
		height: 20rpx;
	}

	/* 退出登录按钮样式 */
	.logout-section {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 32rpx;
		padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
	}

	.logout-btn {
		width: 100%;
		height: 96rpx;
		background-color: #ffffff;
		border: 2rpx solid #f0f0f0;
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.logout-text {
		font-size: 32rpx;
		color: #333333;
		font-weight: 400;
	}
</style>
