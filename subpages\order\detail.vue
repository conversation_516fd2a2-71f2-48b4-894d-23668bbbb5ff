<template>
	<view class="page">
		<!-- 支付提示文本 - 待支付状态 -->
		<view class="payment-notice-text" v-if="orderInfo.status === '0'">
			<text class="notice-text">请在12:09前完成支付</text>
		</view>

		<!-- 已取消状态提示 -->
		<view class="cancelled-notice-text" v-if="orderInfo.status === '4'">
			<image class="hint-icon" src="/static/images/icon_hint_indent.png" mode="aspectFill"></image>
			<text class="cancelled-text">已取消</text>
		</view>

		<!-- 待发货状态提示 -->
		<view class="shipping-notice-text" v-if="orderInfo.status === '1'">
			<image class="wait-icon" src="/static/images/icon_wait_indent.png" mode="aspectFill"></image>
			<text class="shipping-text">待发货</text>
		</view>

		<!-- 待收货状态提示 -->
		<view class="receiving-notice-text" v-if="orderInfo.status === '2'">
			<image class="wait-icon" src="/static/images/icon_wait_indent.png" mode="aspectFill"></image>
			<text class="receiving-text">待收货</text>
		</view>

		<!-- 已签收状态提示 -->
		<view class="completed-notice-text" v-if="orderInfo.status === '3'">
			<image class="down-icon" src="/static/images/down.png" mode="aspectFill"></image>
			<text class="completed-text">已签收</text>
		</view>

		<!-- 支付信息卡片 - 待支付状态 -->
		<view class="payment-card" v-if="orderInfo.status === '0'">
			<view class="payment-info">
				<text class="payment-label">支付金额</text>
				<text class="payment-amount">¥{{ orderInfo.totalAmount }}</text>
				<text class="payment-desc">为了避免订单取消请尽快支付哦~</text>
			</view>
			<view class="pay-btn" @click="handlePayment">
				立即支付
			</view>
		</view>

		<!-- 已取消状态信息卡片 -->
		<view class="payment-card" v-if="orderInfo.status === '4'">
			<view class="payment-info">
				<text class="cancelled-reason">超时未支付已取消</text>
				<text class="payment-desc">为了避免订单取消请尽快支付哦~</text>
			</view>
			<view class="pay-btn" @click="handleRepurchase">
				再次购买
			</view>
		</view>

		<!-- 待发货状态信息卡片 -->
		<view class="payment-card" v-if="orderInfo.status === '1'">
			<view class="payment-info">
				<text class="shipping-reason">商家正在备货中</text>
				<text class="shipping-desc">备货完成尽快发货~</text>
			</view>
		</view>

		<!-- 待收货状态信息卡片 -->
		<view class="payment-card" v-if="orderInfo.status === '2'">
			<view class="payment-info">
				<text class="receiving-reason">快递正在配送中</text>
				<text class="receiving-desc">备货完后尽快给您配送哦~</text>
			</view>
			<view class="pay-btn" @click="handleConfirmReceive">
				确认收货
			</view>
		</view>

		<!-- 已签收状态信息卡片 -->
		<view class="payment-card" v-if="orderInfo.status === '3'">
			<view class="payment-info">
				<text class="completed-reason">订单已签收</text>
				<text class="completed-desc">感谢您的信任与支持~</text>
			</view>
		</view>

		<!-- 商品信息 -->
		<view class="section">
			<view class="section-title">商品信息</view>
			<view class="product-item">
				<image class="product-image" :src="orderInfo.productImage" mode="aspectFill"></image>
				<view class="product-info">
					<text class="product-name">{{ orderInfo.productName }}</text>
					<!-- <text class="product-spec" v-if="orderInfo.specification">规格：{{ orderInfo.specification }}</text> -->
					<view class="product-price-row">
						<text class="product-price">¥{{ orderInfo.productPrice }}</text>
						<text class="product-quantity">x{{ orderInfo.quantity }}</text>
					</view>
				</view>
			</view>
			<view class="total-row">
				<text class="total-label">合计：</text>
				<text class="total-amount">¥{{ orderInfo.totalAmount }}</text>
			</view>
		</view>

		<!-- 价格明细 -->
		<view class="section">
			<view class="section-title">价格明细</view>
			<view class="price-detail">
				<view class="price-row">
					<text class="price-label">商品金额</text>
					<text class="price-value">¥ {{ orderInfo.productTotal }}</text>
				</view>
				<view class="price-row">
					<text class="price-label">运费</text>
					<text class="price-label-yunfei">¥ {{ orderInfo.shippingFee }}</text>
				</view>
				<view class="price-row total">
					<text class="price-label">合计</text>
					<text class="price-value">¥ {{ orderInfo.finalTotal }}</text>
				</view>
			</view>
		</view>

		<!-- 快递信息 -->
		<view class="section">
			<view class="section-title">快递信息</view>
			<view class="express-info">
				<view class="express-row">
					<text class="express-label">配送方式</text>
					<text class="express-value">快递配送</text>
				</view>
				<view class="express-row" v-if="orderInfo.expressCompany !== '暂无物流信息'">
					<text class="express-label">快递公司</text>
					<text class="express-value">{{ orderInfo.expressCompany }}</text>
				</view>
				<view class="express-row" v-if="orderInfo.expressNo !== '暂无物流信息'">
					<text class="express-label">快递单号</text>
					<text class="express-value">{{ orderInfo.expressNo }}</text>
				</view>
				<view class="express-row" v-if="orderInfo.deliveryTime !== '暂未发货'">
					<text class="express-label">发货时间</text>
					<text class="express-value">{{ orderInfo.deliveryTime }}</text>
				</view>
				<view class="express-row address">
					<text class="express-label">收货地址</text>
					<text class="express-value">{{ orderInfo.address }}</text>
				</view>
			</view>
		</view>

		<!-- 订单信息 -->
		<view class="section">
			<view class="section-title">订单信息</view>
			<view class="order-info">
				<view class="info-row">
					<text class="info-label">订单号</text>
					<text class="info-value">{{ orderInfo.orderTime }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">下单时间</text>
					<text class="info-value">{{ orderInfo.createTime }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">支付方式</text>
					<text class="info-value">{{ orderInfo.paymentMethod }}</text>
				</view>
				<view class="info-row" v-if="orderInfo.paymentTime !== '暂未支付'">
					<text class="info-label">支付时间</text>
					<text class="info-value">{{ orderInfo.paymentTime }}</text>
				</view>
				<view class="info-row" v-if="orderInfo.confirmTime !== '暂未确认收货'">
					<text class="info-label">确认收货时间</text>
					<text class="info-value">{{ orderInfo.confirmTime }}</text>
				</view>
				<view class="info-row" v-if="orderInfo.remark !== '无备注'">
					<text class="info-label">订单备注</text>
					<text class="info-value">{{ orderInfo.remark }}</text>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<!-- 待支付状态：显示取消订单和联系客服 -->
		<view class="bottom-actions" v-if="orderInfo.status === '0'">
			<view class="action-btn secondary" @click="cancelOrder">
				取消订单
			</view>
			<view class="action-btn primary" @click="contactService">
				联系客服
			</view>
		</view>

		<!-- 待发货状态：只显示联系客服 -->
		<view class="bottom-actions" v-if="orderInfo.status === '1'">
			<view class="action-btn primary" @click="contactService">
				联系客服
			</view>
		</view>

		<!-- 待收货状态：只显示联系客服 -->
		<view class="bottom-actions" v-if="orderInfo.status === '2'">
			<view class="action-btn primary" @click="contactService">
				联系客服
			</view>
		</view>

		<!-- 已签收状态：只显示联系客服 -->
		<view class="bottom-actions" v-if="orderInfo.status === '3'">
			<view class="action-btn primary" @click="contactService">
				联系客服
			</view>
		</view>

		<!-- 已取消状态：只显示联系客服 -->
		<view class="bottom-actions" v-if="orderInfo.status === '4'">
			<view class="action-btn primary" @click="contactService">
				联系客服
			</view>
		</view>
	</view>
</template>

<script>
import { orderApi } from '@/api/index.js'

export default {
	data() {
		return {
			orderNumber: '',
			status: '',
			goodsId: '', // 商品ID，用于再次购买
			orderInfo: {
				status: '3', // 使用数字状态码：0待支付 1待发货 2待收货 3已签收 4已取消
				totalAmount: '19',
				productImage: '/static/images/icon_weixin_kind.png',
				productName: '名称名称名称名称名称名称名称名称名称名称名称',
				productPrice: '288.00',
				quantity: 1,
				productTotal: '123.12',
				shippingFee: '0',
				finalTotal: '59.97',
				address: '山西省太原市小店区龙城北街北小店区龙城北街313号三单元1-3',
				orderTime: '202505237865123',
				createTime: '2025-05-23 18:12:11',
				paymentMethod: '微信支付',
				paymentTime: '2025-05-23 18:12:19',
				remark: '来对请打电话，避免跑空'
			}
		}
	},
	onLoad(options) {
		if (options.orderNumber) {
			this.orderNumber = options.orderNumber;
		}
		if (options.status) {
			this.status = options.status;
			this.orderInfo.status = options.status;
		}
		this.loadOrderDetail();
	},
	methods: {
		async loadOrderDetail() {
			if (!this.orderNumber) {
				console.error('订单号不能为空');
				uni.showToast({
					title: '订单号异常',
					icon: 'none'
				});
				return;
			}

			try {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});

				console.log('加载订单详情:', this.orderNumber);
				const result = await orderApi.getOrderDetail(this.orderNumber);

				uni.hideLoading();

				if (result.code === 0 || result.code === 200) {
					console.log('订单详情响应数据:', result.data);
					// 映射订单数据
					this.mapOrderData(result.data);
				} else {
					uni.showToast({
						title: result.msg || '加载失败',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('加载订单详情失败:', error);
				uni.showToast({
					title: '加载失败，请重试',
					icon: 'none'
				});
			}
		},

		// 映射订单数据
		mapOrderData(data) {
			// 保存商品ID用于再次购买
			this.goodsId = data.goodsId;

			this.orderInfo = {
				status: data.orderStatus, // 订单状态
				totalAmount: data.totalAmt, // 总金额
				productImage: data.goodsImg, // 商品图片
				productName: data.goodsName, // 商品名称
				productPrice: data.price, // 商品价格
				quantity: data.goodsNum, // 商品数量
				productTotal: (data.price * data.goodsNum).toFixed(2), // 商品总价
				shippingFee: data.freight || 0, // 运费
				finalTotal: data.totalAmt, // 最终总价
				address: data.address || '暂无收货地址', // 收货地址
				orderTime: data.orderNo, // 订单号
				createTime: this.formatTime(data.createDate), // 下单时间
				paymentMethod: data.payOrderNo ? '微信支付' : '暂未支付', // 支付方式
				paymentTime: data.payTime ? this.formatTime(data.payTime) : '暂未支付', // 支付时间
				remark: data.remarks || '无备注', // 订单备注
				specification: data.specification, // 商品规格
				expressCompany: data.expressCompany || '暂无物流信息', // 快递公司
				expressNo: data.expressNo || '暂无物流信息', // 快递单号
				deliveryTime: data.deliveryTime ? this.formatTime(data.deliveryTime) : '暂未发货', // 发货时间
				confirmTime: data.confirmTime ? this.formatTime(data.confirmTime) : '暂未确认收货' // 确认收货时间
			};
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			const date = new Date(timeStr);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},

		handlePayment() {
			uni.showToast({
				title: '跳转支付页面',
				icon: 'none'
			});
		},

		handleRepurchase() {
			// 获取商品ID
			const goodsId = this.goodsId;

			if (!goodsId) {
				uni.showToast({
					title: '商品信息异常',
					icon: 'none'
				});
				return;
			}

			// 跳转到商品详情页面
			uni.navigateTo({
				url: `/subpages/product/detail?goodsId=${goodsId}`
			});
		},

		handleConfirmReceive() {
			uni.showModal({
				title: '确认收货',
				content: '确定已收到商品吗？',
				success: async (res) => {
					if (res.confirm) {
						await this.confirmReceiptRequest();
					}
				}
			});
		},

		// 确认收货请求
		async confirmReceiptRequest() {
			if (!this.orderNumber) {
				uni.showToast({
					title: '订单号异常',
					icon: 'none'
				});
				return;
			}

			try {
				uni.showLoading({
					title: '确认中...',
					mask: true
				});

				const result = await orderApi.confirmReceipt(this.orderNumber);

				uni.hideLoading();

				if (result.code === 0 || result.code === 200) {
					uni.showToast({
						title: '确认收货成功',
						icon: 'success'
					});

					// 重新加载订单详情
					setTimeout(() => {
						this.loadOrderDetail();
					}, 1500);
				} else {
					uni.showToast({
						title: result.msg || '确认收货失败，请重试',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('确认收货失败:', error);
				uni.showToast({
					title: '确认收货失败，请重试',
					icon: 'none'
				});
			}
		},
		
		cancelOrder() {
			uni.showModal({
				title: '提示',
				content: '确定要取消此订单吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '取消成功',
							icon: 'success'
						});
					}
				}
			});
		},
		
		contactService() {
			uni.showToast({
				title: '联系客服',
				icon: 'none'
			});
		}
	}
}
</script>

<style scoped>
.page {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
}

/* 支付提示文本 */
.payment-notice-text {
	padding: 24rpx 32rpx;
	background-color: #f5f5f5;
}

.notice-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

/* 已取消状态提示 */
.cancelled-notice-text {
	padding: 24rpx 32rpx;
	background-color: #f5f5f5;
	display: flex;
	align-items: center;
}

.hint-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 16rpx;
}

.cancelled-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

/* 待发货状态提示 */
.shipping-notice-text {
	padding: 24rpx 32rpx;
	background-color: #f5f5f5;
	display: flex;
	align-items: center;
}

.wait-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 16rpx;
}

.shipping-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

/* 待收货状态提示 */
.receiving-notice-text {
	padding: 24rpx 32rpx;
	background-color: #f5f5f5;
	display: flex;
	align-items: center;
}

.receiving-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

/* 已完成状态提示 */
.completed-notice-text {
	padding: 24rpx 32rpx;
	background-color: #f5f5f5;
	display: flex;
	align-items: center;
}

.down-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 16rpx;
}

.completed-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

/* 支付信息卡片 */
.payment-card {
	background-color: #ffffff;
	margin: 24rpx 32rpx;
	border-radius: 16rpx;
	padding: 32rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.payment-info {
	display: flex;
	align-items: baseline;
	flex-wrap: wrap;
	flex: 1;
}

.payment-label {
	font-size: 28rpx;
	color: #666666;
	margin-right: 8rpx;
}

.payment-amount {
	font-size: 32rpx;
	color: #ff4444;
	font-weight: 500;
	margin-right: 16rpx;
	font-weight: bold;
}

.payment-desc {
	font-size: 24rpx;
	color: #999999;
	flex-basis: 100%;
	margin-top: 8rpx;
}

.cancelled-reason {
	font-size: 28rpx;
	color: #666666;
	flex-basis: 100%;
	margin-bottom: 8rpx;
}

.shipping-reason {
	font-size: 28rpx;
	color: #666666;
	flex-basis: 100%;
	margin-bottom: 8rpx;
}

.shipping-desc {
	font-size: 24rpx;
	color: #999999;
	flex-basis: 100%;
}

.receiving-reason {
	font-size: 28rpx;
	color: #666666;
	flex-basis: 100%;
	margin-bottom: 8rpx;
}

.receiving-desc {
	font-size: 24rpx;
	color: #999999;
	flex-basis: 100%;
}

.completed-reason {
	font-size: 28rpx;
	color: #666666;
	flex-basis: 100%;
	margin-bottom: 8rpx;
}

.completed-desc {
	font-size: 24rpx;
	color: #999999;
	flex-basis: 100%;
}

.pay-btn {
	background-color: #4D40E5;
	color: #ffffff;
	padding: 16rpx 32rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	white-space: nowrap;
}

/* 通用区块样式 */
.section {
	background-color: #ffffff;
	margin: 24rpx 32rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.section-title {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
	margin-bottom: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
	padding-bottom: 24rpx;
}

/* 商品信息 */
.product-item {
	display: flex;
	margin-bottom: 24rpx;
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	margin-right: 24rpx;
}

.product-info {
	flex: 1;
}

.product-name {
	font-size: 28rpx;
	color: #333333;
	line-height: 40rpx;
	margin-bottom: 8rpx;
	display: block;
}

.product-spec {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 16rpx;
}

.product-price-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.product-price {
	font-size: 32rpx;
	color: #ff4444;
	font-weight: 500;
}

.product-quantity {
	font-size: 28rpx;
	color: #666666;
}

.total-row {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding-top: 24rpx;
	border-top: 1rpx solid #f0f0f0;
}

.total-label {
	font-size: 28rpx;
	color: #333333;
	margin-right: 8rpx;
}

.total-amount {
	font-size: 32rpx;
	color: #ff4444;
	font-weight: 500;
}

/* 价格明细 */
.price-detail {
	padding: 0;
}

.price-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.price-row.total {
	padding-top: 16rpx;
	margin-bottom: 0;
}

.price-label {
	font-size: 28rpx;
	color: #666666;
}

.price-row.total .price-label {
	color: #333333;
	font-weight: 500;
}

.price-value {
	font-size: 28rpx;
	color: #ff4444;
	font-weight: bold;
}

.price-label-yunfei {
	color: #333333;
	font-size: 28rpx;
	font-weight: bold;
}

.price-row.total .price-value {
	color: #ff4444;
	font-weight: 500;
	font-size: 32rpx;
	font-weight: bold;
}

/* 快递信息 */
.express-info {
	padding: 0;
}

.express-row {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.express-row:last-child {
	margin-bottom: 0;
}

.express-row.address {
	align-items: flex-start;
}

.express-label {
	font-size: 28rpx;
	color: #666666;
	flex-shrink: 0;
	width: 160rpx;
}

.express-value {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
	text-align: right;
}

.express-row.address .express-value {
	text-align: right;
	line-height: 40rpx;
}

/* 订单信息 */
.order-info {
	padding: 0;
}

.info-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 28rpx;
	color: #666666;
	flex-shrink: 0;
	width: 160rpx;
}

.info-value {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
	text-align: right;
}

/* 底部按钮 */
.bottom-actions {
	
	padding: 24rpx 32rpx;
	display: flex;
	gap: 24rpx;
	justify-content: flex-end;
}

.action-btn {
	padding: 20rpx 50rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	text-align: center;
}

.action-btn.primary {
	background-color: #4D40E5;
	color: #ffffff;
}

.action-btn.secondary {
	background-color: #ffffff;
	color: #666666;
	border: 2rpx solid #e0e0e0;
}
</style>
