<template>
	<view class="address-edit-page">
		<form @submit="formSubmit">
			<!-- 收货人信息 -->
			<view class="form-section">
				<view class="form-item">
					<view class="label">收货人</view>
					<input 
						class="input" 
						type="text" 
						name="realName"
						:value="addressData.realName"
						placeholder="请输入收货人姓名" 
						placeholder-style="color: #CCCCCC"
						maxlength="20"
					/>
				</view>
				
				<view class="form-item">
					<view class="label">手机号码</view>
					<input 
						class="input" 
						type="number" 
						name="phone"
						:value="addressData.phone"
						placeholder="请输入收货人手机号" 
						placeholder-style="color: #CCCCCC"
						maxlength="11"
					/>
				</view>
			</view>
			
			<!-- 地址信息 -->
			<view class="form-section">
				<view class="form-item" @click="selectRegion">
					<view class="label">所在地区</view>
					<view class="region-selector">
						<text v-if="!regionText" class="placeholder">省市区县/乡镇等</text>
						<text v-else class="region-text">{{ regionText }}</text>
						<uni-icons type="location" size="16" color="#4D40E5"></uni-icons>
					</view>
				</view>
				
				<view class="form-item">
					<view class="label">详细地址</view>
					<input 
						class="input" 
						type="text" 
						name="detail"
						v-model="addressData.detail"
						placeholder="街道/楼牌号等" 
						placeholder-style="color: #CCCCCC"
						maxlength="100"
					/>
				</view>
			</view>
			
			<!-- 默认地址设置 -->
			<view class="form-section">
				<view class="default-section">
					<view class="default-content">
						<view class="default-title">设置默认收货地址</view>
						<view class="default-tip">提醒：下单会优先使用该地址</view>
					</view>
					<switch
						:checked="addressData.isDefault"
						color="#4D40E5"
						@change="onSwitchChange"
					/>
				</view>
			</view>
			
			<!-- 底部按钮 -->
			<view class="bottom-actions">
				<button v-if="addressId" class="delete-btn" @click="deleteAddress">删除</button>
				<button class="save-btn" form-type="submit" :class="{'full-width': !addressId}">
					保存并使用
				</button>
			</view>
		</form>
		
		<!-- 地区选择弹窗 -->
		<areaWindow 
			ref="areaWindow" 
			:display="showRegionPicker" 
			:address="regionInfo" 
			@submit="onRegionSelect"
			@changeClose="closeRegionPicker" 
		/>
	</view>
</template>

<script>
import areaWindow from '@/components/areaWindow/index.vue'
import { addressApi } from '@/api/index.js'

export default {
	components: {
		areaWindow
	},
	
	data() {
		return {
			addressId: 0,
			addressData: {
				realName: '',
				phone: '',
				province: '',
				city: '',
				district: '',
				detail: '',
				isDefault: false
			},
			regionInfo: [],
			showRegionPicker: false
		}
	},
	
	computed: {
		regionText() {
			if (this.regionInfo.length > 0) {
				return this.regionInfo.map(item => item.regionName).join('');
			}
			return '';
		}
	},
	
	onLoad(options) {
		this.addressId = options.id || 0;
		
		// 设置导航栏标题
		uni.setNavigationBarTitle({
			title: this.addressId ? '编辑地址' : '新建地址'
		});
		
		if (this.addressId) {
			this.loadAddressDetail();
		}
	},
	
	methods: {
		// 加载地址详情
		async loadAddressDetail() {
			try {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});

				const res = await addressApi.getAddressById(this.addressId);

				uni.hideLoading();

				if (res.code === 0 && res.data) {
					const addressData = res.data;

					// 设置表单数据
					this.addressData = {
						realName: addressData.realName || '',
						phone: addressData.contactPhone || '',
						province: '',
						city: '',
						district: '',
						detail: this.extractDetailFromFullAddress(addressData.detailAddress || ''),
						isDefault: addressData.isDefault === "1"
					};

					// 根据省市区代码设置地区信息（这里需要根据实际情况处理）
					// 由于detailAddress包含完整地址，我们需要解析或者通过其他方式获取省市区信息
					this.regionInfo = await this.getRegionInfoByCodes(
						addressData.provinceCode,
						addressData.cityCode,
						addressData.areaCode
					);
				}
			} catch (error) {
				uni.hideLoading();
				console.error('获取地址详情失败:', error);
				uni.showToast({
					title: '获取地址详情失败',
					icon: 'none'
				});
			}
		},

		// 从完整地址中提取详细地址部分
		extractDetailFromFullAddress(fullAddress) {
			// 假设格式为：省市区 详细地址
			// 找到第一个空格，空格后面的就是详细地址
			const spaceIndex = fullAddress.indexOf(' ');
			if (spaceIndex > -1) {
				return fullAddress.substring(spaceIndex + 1);
			}
			return fullAddress;
		},

		// 根据省市区代码获取地区信息
		async getRegionInfoByCodes(provinceCode, cityCode, areaCode) {
			try {
				// 这里需要调用省市区接口，根据代码获取名称
				// 由于当前省市区接口返回的是完整数据，我们需要从中查找对应的名称
				const res = await addressApi.getRegions();
				if (res.code === 0) {
					const regionInfo = [];

					// 查找省份
					const province = res.data.find(p => p.code === provinceCode);
					if (province) {
						regionInfo.push({
							regionId: province.id,
							regionName: province.name,
							regionCode: province.code,
							regionType: 1
						});

						// 查找城市
						if (province.posCityList) {
							const city = province.posCityList.find(c => c.code === cityCode);
							if (city) {
								regionInfo.push({
									regionId: city.id,
									regionName: city.name,
									regionCode: city.code,
									regionType: 2
								});

								// 查找区县
								if (city.posCountryList) {
									const area = city.posCountryList.find(a => a.code === areaCode);
									if (area) {
										regionInfo.push({
											regionId: area.id,
											regionName: area.name,
											regionCode: area.code,
											regionType: 3
										});
									}
								}
							}
						}
					}

					return regionInfo;
				}
			} catch (error) {
				console.error('获取地区信息失败:', error);
			}
			return [];
		},
		
		// 选择地区
		selectRegion() {
			this.showRegionPicker = true;
		},
		
		// 地区选择完成
		onRegionSelect(regions) {
			this.regionInfo = regions;
			if (regions.length > 0) {
				this.addressData.province = regions[0]?.regionName || '';
				this.addressData.city = regions[1]?.regionName || '';
				this.addressData.district = regions[2]?.regionName || '';
			}
			this.showRegionPicker = false;
		},
		
		// 关闭地区选择器
		closeRegionPicker() {
			this.showRegionPicker = false;
		},
		
		// 开关变化
		onSwitchChange(e) {
			this.addressData.isDefault = e.detail.value;
		},
		
		// 删除地址
		async deleteAddress() {
			uni.showModal({
				title: '提示',
				content: '确定删除该地址吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({
								title: '删除中...',
								mask: true
							});

							const result = await addressApi.deleteAddress(this.addressId);

							uni.hideLoading();

							if (result.code === 0) {
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
								setTimeout(() => {
									uni.navigateBack();
								}, 1500);
							}
						} catch (error) {
							uni.hideLoading();
							console.error('删除地址失败:', error);
							uni.showToast({
								title: '删除失败，请重试',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 表单提交
		async formSubmit(e) {
			const formData = e.detail.value;

			// 表单验证
			if (!formData.realName) {
				uni.showToast({
					title: '请输入收货人姓名',
					icon: 'none'
				});
				return;
			}

			if (!formData.phone) {
				uni.showToast({
					title: '请输入手机号码',
					icon: 'none'
				});
				return;
			}

			if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
				uni.showToast({
					title: '请输入正确的手机号码',
					icon: 'none'
				});
				return;
			}

			if (!this.regionText) {
				uni.showToast({
					title: '请选择所在地区',
					icon: 'none'
				});
				return;
			}

			if (!formData.detail) {
				uni.showToast({
					title: '请输入详细地址',
					icon: 'none'
				});
				return;
			}

			// 构造完整的详细地址：省市区 + 空格 + 详细地址
			const regionText = this.regionInfo.map(item => item.regionName).join('');
			const fullDetailAddress = regionText + ' ' + formData.detail;

			// 构造提交数据
			const submitData = {
				realName: formData.realName,
				contactPhone: formData.phone,
				detailAddress: fullDetailAddress,
				isDefault: this.addressData.isDefault ? "1" : "0",
				provinceCode: this.regionInfo[0]?.regionCode || '',
				cityCode: this.regionInfo[1]?.regionCode || '',
				areaCode: this.regionInfo[2]?.regionCode || '',
				remarks: '',
				userId: uni.getStorageSync('userInfo').id,
				tenantId: 3
			};

			// 如果是编辑模式，添加id
			if (this.addressId) {
				submitData.id = this.addressId;
			}

			try {
				uni.showLoading({
					title: '保存中...',
					mask: true
				});

				let res;
				if (this.addressId) {
					// 编辑地址
					res = await addressApi.updateAddress(submitData);
				} else {
					// 新增地址
					res = await addressApi.addAddress(submitData);
				}

				uni.hideLoading();

				if (res.code === 0) {
					uni.showToast({
						title: this.addressId ? '修改成功' : '添加成功',
						icon: 'success'
					});

					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			} catch (error) {
				uni.hideLoading();
				console.error('保存地址失败:', error);
				uni.showToast({
					title: '保存失败，请重试',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.address-edit-page {
	min-height: 100vh;
	background-color: #F5F5F5;
	padding: 20rpx 30rpx 140rpx;
}

.form-section {
	background-color: #ffffff;
	border-radius: 14rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
}

.form-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1px solid #F5F5F5;
	
	&:last-child {
		border-bottom: none;
	}
	
	.label {
		width: 160rpx;
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
	}
	
	.input {
		flex: 1;
		font-size: 30rpx;
		color: #333333;
		text-align: right;
	}
	
	.region-selector {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		
		.placeholder {
			font-size: 30rpx;
			color: #CCCCCC;
		}
		
		.region-text {
			font-size: 30rpx;
			color: #333333;
			margin-right: 10rpx;
		}
		

	}
}

.default-section {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	
	.default-content {
		flex: 1;
		
		.default-title {
			font-size: 30rpx;
			color: #333333;
			font-weight: 500;
			margin-bottom: 8rpx;
		}
		
		.default-tip {
			font-size: 24rpx;
			color: #999999;
		}
	}
}

.bottom-actions {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 20rpx 30rpx;
	background-color: #F5F5F5;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	display: flex;
	gap: 20rpx;
	
	.delete-btn {
		flex: 1;
		height: 88rpx;
		background-color: #ffffff;
		border: 1px solid #EEEEEE;
		border-radius: 20rpx;
		font-size: 32rpx;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.save-btn {
		flex: 2;
		height: 88rpx;
		background: #4D40E5;
		border-radius: 20rpx;
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		border: none;
		
		&.full-width {
			flex: 1;
		}
	}
}
</style>
