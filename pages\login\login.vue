<template>
	<view class="login-container">
		<!-- 顶部背景区域 -->
		<view class="top-bg-section">
			<!-- 背景图片 -->
			<image class="bg-image" src="/static/images/bg_home.png" mode="aspectFill"></image>
			
			<!-- 内容区域 -->
			<view class="content-overlay">
				<!-- 左侧文字 -->
				<view class="text-section">
					<view class="title">乐腾生态 分佣体系</view>
					<view class="subtitle">分佣体系更明了</view>
				</view>
				
				<!-- 右侧图标 -->
				<image class="logo-icon" src="/static/images/img_ceding_login.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 下半部分内容 -->
		<view class="bottom-section">
			<!-- Tab和表单整体容器 -->
			<view class="tab-form-container">
				<!-- Tab切换 -->
				<view class="tab-container">
					<view
						class="tab-item"
						:class="activeTab === 'agent' ? 'tab-active' : ''"
						@click="switchTab('agent')"
					>
						<text class="tab-text">代理商</text>
						<image
							v-if="activeTab === 'agent'"
							class="select-line"
							src="/static/images/icon_option_select.png"
							mode="aspectFit"
						></image>
					</view>

					<view
						class="tab-item"
						:class="activeTab === 'team' ? 'tab-active' : ''"
						@click="switchTab('team')"
					>
						<text class="tab-text">团队</text>
						<image
							v-if="activeTab === 'team'"
							class="select-line"
							src="/static/images/icon_option_select.png"
							mode="aspectFit"
						></image>
					</view>

					<view
						class="tab-item"
						:class="activeTab === 'employee' ? 'tab-active' : ''"
						@click="switchTab('employee')"
					>
						<text class="tab-text">员工</text>
						<image
							v-if="activeTab === 'employee'"
							class="select-line"
							src="/static/images/icon_option_select.png"
							mode="aspectFit"
						></image>
					</view>
				</view>

				<!-- 登录表单 -->
				<view class="form-container">
					<view class="input-group">
						<input
							class="input-field"
							type="text"
							placeholder="请输入手机号"
							v-model="loginForm.phone"
						/>
					</view>

					<view class="input-group">
						<input
							class="input-field"
							type="password"
							placeholder="请输入密码"
							v-model="loginForm.passwd"
						/>
					</view>

					<view class="forgot-password">
						<text @click="handleForgotPassword">忘记密码</text>
					</view>

					<button class="login-btn" @click="handleLogin">登录</button>
				</view>
			</view>
			
			<!-- 底部协议 -->
			<view class="agreement-section">
				<view class="agreement-text" @click="toggleAgreement">
					<radio
						:checked="isAgreed"
						color="#6366F1"
						class="agreement-radio"
					/>
					<text class="agreement-label">我已阅读并同意乐腾生态《用户协议》《隐私政策》</text>
				</view>
			</view>
		</view>
		
		<!-- 底部指示器 -->
		<view class="bottom-indicator"></view>
	</view>
</template>

<script>
import { userApi } from '@/api/index.js';

export default {
	data() {
		return {
			statusBarHeight: 0,
			activeTab: 'agent', // 默认选中代理商
			loginForm: {
				phone: '',
				passwd: '',
				tenantId: 1 // 1=代理商，2=团队，3=员工
			},
			isAgreed: false // 协议同意状态
		}
	},
	
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
	},
	
	methods: {
		// 切换Tab
		switchTab(tab) {
			this.activeTab = tab;
			// 根据选中的tab设置tenantId
			switch(tab) {
				case 'agent':
					this.loginForm.tenantId = 1; // 代理商
					break;
				case 'team':
					this.loginForm.tenantId = 2; // 团队
					break;
				case 'employee':
					this.loginForm.tenantId = 3; // 员工
					break;
			}
		},
		
		// 登录处理
		async handleLogin() {
			if (!this.loginForm.phone) {
				uni.showToast({
					title: '请输入手机号',
					icon: 'none'
				});
				return;
			}

			if (!this.loginForm.passwd) {
				uni.showToast({
					title: '请输入密码',
					icon: 'none'
				});
				return;
			}

			if (!this.isAgreed) {
				uni.showToast({
					title: '请先同意用户协议和隐私政策',
					icon: 'none'
				});
				return;
			}

			try {
				// 调用登录接口
				const res = await userApi.login({
					passwd: this.loginForm.passwd,
					phone: this.loginForm.phone,
					tenantId: 3
				});

				// 登录成功，存储token
				if (res.data && res.data.token) {
					uni.setStorageSync('token', res.data.token);

					// 存储用户信息
					if (res.data.user) {
						uni.setStorageSync('userInfo', res.data.user);
					}

					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});

					// 登录成功后跳转
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/index/index'
						});
					}, 1500);
				} else {
					uni.showToast({
						title: res.msg || '登录失败，请重试',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('登录失败:', error);
				// 错误提示已经在请求拦截器中处理了，这里不需要再次提示
			}
		},

		// 切换协议同意状态
		toggleAgreement() {
			this.isAgreed = !this.isAgreed;
		},
		
		// 忘记密码
		handleForgotPassword() {
			uni.navigateTo({
				url: '/subpages/auth/forgot-password'
			});
		}
	}
}
</script>

<style scoped>
.login-container {
	width: 100%;
	height: 100vh;
	background-color: #F6F8FF;
}

.status-bar {
	width: 100%;
	background-color: transparent;
}

/* 顶部背景区域 */
.top-bg-section {
	position: relative;
	width: 100%;
	height: 600rpx;
}

.bg-image {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.content-overlay {
	position: relative;
	z-index: 2;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 80rpx 20rpx 0;
	height: 100%;
}

.text-section {
	flex: 1;
}

.title {
	font-size: 49rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 20rpx;
	line-height: 1.2;
}

.subtitle {
	font-size: 32rpx;
	color: #666666;
	opacity: 0.8;
}

.logo-icon {
	width: 300rpx;
	height: 300rpx;
}

/* 下半部分 */
.bottom-section {
	flex: 1;
	background-color: #F6F8FF;
	padding: 0 30rpx;
}

/* Tab和表单整体容器 */
.tab-form-container {
	background-color: #FFFFFF;
	border-radius: 24rpx;
	overflow: visible; /* 改为visible让选中的tab可以向上突出 */
	margin-bottom: 80rpx;
	position: relative;
}

/* Tab切换 */
.tab-container {
	display: flex;
	background-color: #E9ECFE;
	position: relative;
	height: 80rpx; /* 固定高度防止抖动 */
	padding-top: 16rpx; /* 为选中Tab向上突出预留空间 */
	border-radius: 20rpx 20rpx 0 0;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 24rpx 0;
	position: relative;
	/* transition: all 0.3s ease; */
	cursor: pointer;
	min-height: 56rpx; /* 最小高度 */
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	z-index: 999;
}

/* 选中状态的Tab */
.tab-item.tab-active {
	background-color: #FFFFFF;
	padding: 32rpx 0;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;
	position: relative;
	z-index: 2;
	min-height: 72rpx; /* 选中时高度增加 */
	margin-top: -40rpx; /* 向上突出，不影响容器高度 */
}



.tab-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
	pointer-events: none; /* 防止文字阻挡点击事件 */
}

.select-line {
	position: absolute;
	bottom: 8rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 155rpx;
	height: 30rpx;
	pointer-events: none; /* 防止图片阻挡点击事件 */
}

/* 登录表单 */
.form-container {
	background-color: #FFFFFF;
	padding: 60rpx 40rpx;
	position: relative;
	z-index: 1;
	margin-top: 0; /* 确保不受Tab高度变化影响 */
	border-radius: 0 0 20rpx 20rpx;
}

.input-group {
	margin-bottom: 40rpx;
}

.input-field {
	height: 96rpx;
	background-color: #F8F9FF;
	border-radius: 16rpx;
	padding: 0 32rpx;
	font-size: 32rpx;
	color: #333333;
	border: none;
}

.input-field::placeholder {
	color: #CCCCCC;
}

.forgot-password {
	text-align: right;
	margin-bottom: 60rpx;
}

.forgot-password text {
	font-size: 28rpx;
	color: #999999;
}

.login-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
	border-radius: 20rpx;
	border: none;
	color: #FFFFFF;
	font-size: 36rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

.login-btn:active {
	opacity: 0.8;
}

/* 底部协议 */
.agreement-section {
	position: fixed;
	bottom: 120rpx;
	left: 60rpx;
	right: 60rpx;
}

.agreement-text {
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #999999;
	cursor: pointer;
}

.agreement-radio {
	margin-right: 12rpx;
	transform: scale(0.8);
}

.agreement-label {
	font-size: 24rpx;
	color: #999999;
}

/* 底部指示器 */
.bottom-indicator {
	position: fixed;
	bottom: 20rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 280rpx;
	height: 8rpx;
	background-color: #000000;
	border-radius: 4rpx;
}
</style>
