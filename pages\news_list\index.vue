<template>
	<view class="" style="height: 100%;">
		<mescroll-uni :fixed="false" @init="mescrollInit" :height="height" :down="downOption" @down="downCallback"
			:up="upOption" @up="upCallback" @emptyclick="emptyClick">
			<view class="news-item" v-for="(item,index) in goods" :key="index" @click="toDetails(index)">
				<view class="news-content">
					<text class="news-title">{{item.title}}</text>
					<text class="news-intro">{{item.intro}}</text>
					<text class="news-date">{{item.createDate}}</text>
				</view>
				<image class="news-img" :src="item.picUrl" mode="aspectFill"></image>
			</view>
		</mescroll-uni>
	</view>
</template>

<script>
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	import { businessSchoolApi } from '@/api/index.js';

	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				downOption: {
					auto: true //  false 不自动加载 (mixin已处理第一个tab触发downCallback)  true
				},
				upOption: {
					auto: false, // 不自动加载
					noMoreSize: 4,
					textLoading: '加载中 ...', // 加载中的提示文本
					textNoMore: '暂无更多', // 没有更多数据的提示文本
					empty: {
						tip: '暂无相关数据', // 提示
					},
				},
				goods: [],
			}
		},
		onShow() {

		},
		methods: {
			toDetails(item){
				uni.navigateTo({
					url:'/subpages/news/detail?id='+item
				})
			},
			/*下拉刷新的回调 */
			downCallback() {
				// 这里加载你想下拉刷新的数据, 比如刷新轮播数据
				// loadSwiper();
				// 下拉刷新的回调,默认重置上拉加载列表为第一页 (自动执行 page.num=1, 再触发upCallback方法 )
				this.mescroll.resetUpScroll()
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			async upCallback(page) {
				try {
					let pageNum = page.num; // 页码, 默认从1开始
					let pageSize = page.size; // 页长, 默认每页10条

					console.log('请求参数:', { page: pageNum, limit: pageSize });

					const result = await businessSchoolApi.getPicList({
						page: pageNum,
						limit: pageSize
					});

					console.log('商学院图片列表响应数据:', result);

					if (result.code === 0 || result.code === 200) {
						// 映射商学院图片数据
						const dataList = (result.data || []).map(item => {
							return {
								id: item.id,
								title: item.title,
								intro: item.intro,
								picUrl: item.picUrl,
								picEditor: item.picEditor,
								createDate: this.formatTime(item.createDate),
								dataCode: item.dataCode
							};
						});

						//联网成功的回调,隐藏下拉刷新和上拉加载的状态;
						// 由于没有总页数信息，使用数据长度判断是否还有更多数据
						let hasMore = dataList.length >= pageSize;
						this.mescroll.endBySize(dataList.length, hasMore);

						//设置列表数据
						if (page.num == 1) this.goods = []; //如果是第一页需手动制空列表
						this.goods = this.goods.concat(dataList); //追加新数据
					} else {
						//联网失败, 结束加载
						this.mescroll.endErr();
						uni.showToast({
							title: result.msg || '加载失败',
							icon: 'none'
						});
					}
				} catch (error) {
					//联网失败, 结束加载
					this.mescroll.endErr();
					console.error('加载商学院图片列表失败:', error);
					uni.showToast({
						title: '加载失败，请重试',
						icon: 'none'
					});
				}
			},
			//点击空布局按钮的回调
			emptyClick() {
				uni.showToast({
					title: '点击了按钮,具体逻辑自行实现'
				})
			},

			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			}
		}
	}
</script>
<style>
	page{
		height: 100%;
	}
</style>
<style lang="scss" scoped>
	.news-item {
		display: flex;
		align-items: center;
		background: white;
		padding: 24rpx;
		margin-bottom: 20rpx;
	}

	.news-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		margin-right: 20rpx;
	}

	.news-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		line-height: 44rpx;
		margin-bottom: 12rpx;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.news-intro {
		font-size: 28rpx;
		color: #666666;
		line-height: 40rpx;
		margin-bottom: 16rpx;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.news-date {
		font-size: 24rpx;
		color: #999999;
		line-height: 32rpx;
	}

	.news-img {
		width: 160rpx;
		height: 120rpx;
		border-radius: 16rpx;
		flex-shrink: 0;
	}
</style>