/**
 * 网络请求封装
 */

// 基础配置
const BASE_URL = 'http://ooseek.iepose.cn'; // 请替换为您的API域名
const TIMEOUT = 10000; // 请求超时时间

// 请求拦截器
const requestInterceptor = (options) => {
	// 添加token
	const token = uni.getStorageSync('token');
	if (token) {
		options.header = {
			...options.header,
			'token': `${token}`
		};
	}
	
	// 添加公共参数
	options.header = {
		'Content-Type': 'application/json',
		...options.header
	};
	
	// 显示加载提示
	if (options.showLoading !== false) {
		uni.showLoading({
			title: '加载中...',
			mask: true
		});
	}
	
	return options;
};

// 响应拦截器
const responseInterceptor = (response, options) => {
	// 隐藏加载提示
	if (options.showLoading !== false) {
		uni.hideLoading();
	}

	const { data, statusCode } = response;

	// HTTP状态码检查
	if (statusCode !== 200) {
		uni.showToast({
			title: `请求失败 ${statusCode}`,
			icon: 'none'
		});
		return Promise.reject(response);
	}

	// 业务状态码检查 - 支持0和200作为成功状态码
	if (data.code !== 0 && data.code !== 200) {
		// token失效，跳转登录
		if (data.code === 401) {
			uni.removeStorageSync('token');
			uni.removeStorageSync('userInfo');
			uni.reLaunch({
				url: '/pages/login/login'
			});
			return Promise.reject(data);
		}

		// 其他错误提示
		if (options.showError !== false) {
			uni.showToast({
				title: data.msg || '请求失败',
				icon: 'none'
			});
		}
		return Promise.reject(data);
	}

	return Promise.resolve(data);
};

// 基础请求方法
const request = (options) => {
	// 处理URL
	if (!options.url.startsWith('http')) {
		options.url = BASE_URL + options.url;
	}
	
	// 设置超时时间
	options.timeout = options.timeout || TIMEOUT;
	
	// 请求拦截
	options = requestInterceptor(options);
	
	return new Promise((resolve, reject) => {
		uni.request({
			...options,
			success: (response) => {
				responseInterceptor(response, options)
					.then(resolve)
					.catch(reject);
			},
			fail: (error) => {
				// 隐藏加载提示
				if (options.showLoading !== false) {
					uni.hideLoading();
				}
				
				// 网络错误提示
				uni.showToast({
					title: '网络连接失败',
					icon: 'none'
				});
				reject(error);
			}
		});
	});
};

// GET请求
export const get = (url, params = {}, options = {}) => {
	return request({
		url,
		method: 'GET',
		data: params,
		...options
	});
};

// POST请求
export const post = (url, data = {}, options = {}) => {
	return request({
		url,
		method: 'POST',
		data,
		...options
	});
};

// PUT请求
export const put = (url, data = {}, options = {}) => {
	return request({
		url,
		method: 'PUT',
		data,
		...options
	});
};

// DELETE请求
export const del = (url, data = {}, options = {}) => {
	return request({
		url,
		method: 'DELETE',
		data,
		...options
	});
};

// 文件上传
export const upload = (url, filePath, fileName = 'file', options = {}) => {
	const token = uni.getStorageSync('token');

	return new Promise((resolve, reject) => {
		uni.uploadFile({
			url: BASE_URL + url,
			filePath,
			name: fileName,
			header: {
				'token': token || '',
				...options.header
			},
			success: (response) => {
				try {
					const data = JSON.parse(response.data);
					if (data.code === 0 || data.code === 200) {
						resolve(data);
					} else {
						uni.showToast({
							title: data.msg || '上传失败',
							icon: 'none'
						});
						reject(data);
					}
				} catch (e) {
					reject(e);
				}
			},
			fail: reject
		});
	});
};

export default {
	get,
	post,
	put,
	del,
	upload
};
