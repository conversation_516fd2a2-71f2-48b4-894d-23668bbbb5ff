<template>
	<view class="page">

		<!-- 表单区域 -->
		<view class="form-section">
			<!-- 银行卡号 -->
			<view class="form-item">
				<view class="form-label">银行卡号</view>
				<input 
					class="form-input" 
					placeholder="请输入银行卡号"
					v-model="cardNumber"
					type="number"
					maxlength="19"
				/>
			</view>

			<!-- 所属银行 -->
			<view class="form-item">
				<view class="form-label">所属银行</view>
				<view class="form-input-wrapper">
					<picker @change="bindPickerChange" :value="arrayIndex" :range="bankList" :disabled="bankList.length === 0">
						<view class="form-input" :class="{ placeholder: arrayIndex === null || arrayIndex === undefined }">
							{{ arrayIndex !== null && arrayIndex !== undefined && bankList[arrayIndex] ? bankList[arrayIndex] : '请选择所属银行' }}
						</view>
					</picker>
					<uni-icons type="right" size="16" color="#999999"></uni-icons>
				</view>
			</view>

			<!-- 身份证号 -->
			<view class="form-item">
				<view class="form-label">身份证号</view>
				<input
					class="form-input"
					placeholder="请输入身份证号"
					v-model="idCard"
					maxlength="18"
				/>
			</view>

			<!-- 开户行姓名 -->
			<view class="form-item">
				<view class="form-label">开户行姓名</view>
				<input
					class="form-input"
					placeholder="请输入此卡开户人姓名"
					v-model="accountName"
				/>
			</view>

			<!-- 预留手机号 -->
			<view class="form-item">
				<view class="form-label">预留手机号</view>
				<input
					class="form-input"
					placeholder="请输入预留手机号"
					v-model="mobile"
					type="number"
					maxlength="11"
				/>
			</view>

			<!-- 所属省市 -->
			<view class="form-item" @click="selectRegion">
				<view class="form-label">所属省市</view>
				<view class="form-input-wrapper">
					<view class="form-input" :class="{ placeholder: !regionText }">
						{{ regionText || '请选择所属省市' }}
					</view>
					<uni-icons type="right" size="16" color="#999999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 注意事项 -->
		<view class="notice-section">
			<text class="notice-text">注意：请准确填写卡信息（仅限银行、非信用卡），确保信息及时到账。</text>
		</view>

		<!-- 确定按钮 -->
		<view class="submit-section">
			<button class="submit-btn"   @click="handleSubmit">
				确定
			</button>
		</view>

		<!-- 地区选择弹窗 -->
		<areaWindow
			ref="areaWindow"
			:display="showRegionPicker"
			:address="regionInfo"
			@submit="onRegionSelect"
			@changeClose="closeRegionPicker"
		/>
	</view>
</template>

<script>
import {
	bankList,
	addSettlement
} from '@/api/user.js';
import { addressApi } from '@/api/index.js';
import areaWindow from '@/components/areaWindow/index.vue';

export default {
	components: {
		areaWindow
	},
	data() {
		return {
			searchText: '', // 搜索文本
			cardNumber: '', // 银行卡号
			selectedBank: '', // 选中的银行
			idCard: '', // 身份证号
			accountName: '', // 开户人姓名
			mobile: '', // 预留手机号
			bankList: [], // 银行名称列表（用于picker显示）
			bankData: [], // 完整的银行数据
			arrayIndex: null, // 选中的银行索引
			regionInfo: [], // 选中的省市信息
			showRegionPicker: false, // 是否显示地区选择器
			isEditMode: false, // 是否为编辑模式
			editCardId: null // 编辑的银行卡ID
		}
	},
	computed: {
		// 地区文本显示
		regionText() {
			if (this.regionInfo.length > 0) {
				return this.regionInfo.map(item => item.regionName).join('');
			}
			return '';
		},

		// 是否可以提交
		canSubmit() {
			return this.cardNumber.length >= 16 &&
				   this.selectedBank &&
				   this.idCard.trim() &&
				   this.accountName.trim() &&
				   this.mobile.trim() &&
				   this.regionText;
		}
	},
	async onLoad(options) {
		await this.getBankList();

		// 处理编辑模式
		if (options.cardData) {
			try {
				const cardData = JSON.parse(decodeURIComponent(options.cardData));
				this.isEditMode = true;
				this.editCardId = cardData.id;

				// 填充表单数据
				this.cardNumber = cardData.bankNo || '';
				this.selectedBank = cardData.bankName || '';
				this.accountName = cardData.accName || '';
				this.mobile = cardData.moblie || '';
				this.idCard = cardData.idCard || '';
				console.log(this.idCard);
				// 设置省市信息
				if (cardData.province || cardData.city) {
					this.regionInfo = [];
					if (cardData.province) {
						this.regionInfo.push({
							regionName: cardData.province,
							regionType: 1
						});
					}
					if (cardData.city) {
						this.regionInfo.push({
							regionName: cardData.city,
							regionType: 2
						});
					}
				}

				// 设置银行选择索引
				setTimeout(() => {
					const bankIndex = this.bankList.findIndex(bank => bank === cardData.bankName);
					if (bankIndex > -1) {
						this.arrayIndex = bankIndex;
					}
				}, 500);

			} catch (error) {
				console.error('解析银行卡数据失败:', error);
			}
		}
	},
	methods: {
		// 搜索输入处理
		onSearchInput(e) {
			const value = e.detail.value;
			// 这里可以实现搜索逻辑
			console.log('搜索:', value);
		},

		// 获取银行列表
		async getBankList() {
			try {
				const res = await bankList();
				if (res.code === 0 && res.data) {
					// 将银行数据转换为显示用的数组，使用 shortCnName 作为显示名称
					this.bankList = res.data.map(item => item.shortCnName || item.bankName);
					// 保存完整的银行数据用于后续使用
					this.bankData = res.data;
				}
			} catch (error) {
				console.error('获取银行列表失败:', error);
				uni.showToast({
					title: '获取银行列表失败',
					icon: 'none'
				});
			}
		},

		// picker选择银行
		bindPickerChange(e) {
			this.arrayIndex = e.detail.value;
			const selectedBankData = this.bankData[e.detail.value];
			// 使用银行的完整名称作为选中的银行
			this.selectedBank = selectedBankData ? selectedBankData.bankName : this.bankList[e.detail.value];
		},

		// 选择地区
		selectRegion() {
			this.showRegionPicker = true;
		},

		// 地区选择完成
		onRegionSelect(regions) {
			this.regionInfo = regions;
			this.showRegionPicker = false;
		},

		// 关闭地区选择器
		closeRegionPicker() {
			this.showRegionPicker = false;
		},



		// 提交表单
		async handleSubmit() {
			if (!this.canSubmit) {
				uni.showToast({
					title: '请完善所有信息',
					icon: 'none'
				});
				return;
			}

			// 手机号验证
			if (!/^1[3-9]\d{9}$/.test(this.mobile)) {
				uni.showToast({
					title: '请输入正确的手机号码',
					icon: 'none'
				});
				return;
			}

			// 身份证号验证
			if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(this.idCard)) {
				uni.showToast({
					title: '请输入正确的身份证号',
					icon: 'none'
				});
				return;
			}

			// 构造提交数据，使用新的API接口字段
			const cardData = {
				accName: this.accountName, // 开户人姓名
				accNo: this.cardNumber, // 银行卡号
				bankName: this.selectedBank, // 银行名称
				idCard: this.idCard, // 身份证号
				moblie: this.mobile, // 预留手机号（注意拼写与API保持一致）
				province: this.regionInfo[0]?.regionName || '', // 省份
				city: this.regionInfo[1]?.regionName || '', // 城市
				bankAccountType: '1', // 银行账户类型，默认为1
				type: '1' // 类型，默认为1
			};

			// 如果是编辑模式，添加ID
			if (this.isEditMode && this.editCardId) {
				cardData.id = this.editCardId;
			}

			try {
				uni.showLoading({
					title: '保存中...'
				});

				const res = await addSettlement(cardData);

				uni.hideLoading();

				if (res.code === 0) {
					uni.showToast({
						title: this.isEditMode ? '修改成功' : '添加成功',
						icon: 'success'
					});

					// 返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}

			} catch (error) {
				uni.hideLoading();
				console.error('保存银行卡失败:', error);
				uni.showToast({
					title: error.message || '保存失败',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style scoped>
.page {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
}

/* 搜索区域 */
.search-section {
	padding: 30rpx;
	background-color: #ffffff;
}

.search-box {
	background-color: #f5f5f5;
	border-radius: 50rpx;
	padding: 0 30rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
}

.search-input::placeholder {
	color: #999999;
}

/* 表单区域 */
.form-section {
	margin: 0 30rpx;
	background-color: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
}

.form-item {
	display: flex;
	align-items: center;
	padding: 40rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
	border-bottom: none;
}

.form-label {
	width: 200rpx;
	font-size: 28rpx;
	color: #333333;
	flex-shrink: 0;
}

.form-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	text-align: right;
}

.form-input.placeholder {
	color: #999999;
}

.form-input-wrapper {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

/* 注意事项 */
.notice-section {
	padding: 30rpx;
}

.notice-text {
	font-size: 24rpx;
	color: #999999;
	line-height: 1.6;
}

/* 提交按钮 */
.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background-color: #4D40E5;
	border-radius: 20rpx;
	border: none;
	color: #ffffff;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn.disabled {
	background-color: #cccccc;
	color: #999999;
}

.submit-btn::after {
	border: none;
}
</style>
