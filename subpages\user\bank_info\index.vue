<template>
	<view :data-theme="theme">
		<view class="withdraw_cash">
			<view class="withdraw_log">
				<view class="withdraw_cash_list list-item" v-for="(item, index) in houseArr" :key="item.id"
					@touchstart="onTouchStart($event, index)" @touchmove="onTouchmove($event, index)"
					@touchend="onTouchEnd($event, index)" :style="{ transform: `translateX(${item.offsetX}px)` }">
					<view class="withdraw_cash_left">
						<view
							style="width: 84rpx;height: 84rpx;border-radius: 50%;background-color: #4D40E5;display: flex;justify-content: center;align-items: center;">
							<img src="/static/images/icon_car_kind.png" style="width: 54rpx;height: 54rpx;" alt="" />
						</view>
						<view style="margin-left: 20rpx;">
							<view style="font-size: 30rpx;">
								<text>{{item.bankName}}</text>
							</view>
							<view style="font-size: 28rpx;color: #999999;margin-top: 10rpx;">
								<text>{{item.bankNo.substring(0,4)+'****'+item.bankNo.substring(item.bankNo.length-4,item.bankNo.length)}}</text>
							</view>
						</view>

						<!-- 编辑按钮 -->
						<view class="edit-btn" @click.stop="editCard(item)">
							<image class="edit-icon" src="/static/images/icon_edit.png" mode="aspectFit"></image>
						</view>
					</view>

					<!-- 删除按钮 -->
					<view class="delete-btn" @click="delClick(`${item.bankName}`, `${item.id}`)">删除</view>
				</view>
			</view>
		</view>
		<view class="container_money_footer">
			<button class="but" @click="addCard">添加银行卡</button>
		</view>
	</view>
</template>

<script>

	import {
		settlementList,
		deleteSettlement
	} from '@/api/user.js';
	import {
		mapGetters
	} from "vuex";
	import {
		Debounce
	} from '@/utils/validate.js'
	let app = getApp();
	export default {
		data() {
			return {
				bankList: [],
				houseArr: [],
				startX: 0, // 触摸起始X坐标
				startY: 0, // 触摸起始Y坐标
				offsetX: 0, // 当前偏移量
				currentI: null

			};
		},
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.userDalance();
					}
				},
				deep: true
			}
		},
		onLoad() {
			let that = this;
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#F5F5F5',
			});
		},
		onShow() {
		
				this.getSettlementList();
		},
		methods: {
			//获取列表数据
			async getSettlementList() {
				try {
					uni.showLoading({
						title: '加载中....',
						mask: true
					});

					const res = await settlementList();

					uni.hideLoading();

					if (res.code === 0) {
						this.houseArr = (res.data || []).map(item => {
							// 解析cardInfo JSON字符串
							let cardInfo = {};
							try {
								cardInfo = JSON.parse(item.cardInfo || '{}');
							} catch (error) {
								console.error('解析cardInfo失败:', error);
								cardInfo = {};
							}

							return {
								id: item.id,
								bankName: cardInfo.bankName || '未知银行',
								bankNo: cardInfo.accNo || '',
								accName: cardInfo.accName || '',
								moblie: cardInfo.moblie || '',
								province: cardInfo.province || '',
								city: cardInfo.city || '',
								bankAccountType: cardInfo.bankAccountType || '',
								branchBankName: cardInfo.branchBankName || '',
								idCard: cardInfo.idCard || '', // 添加身份证号字段
								createDate: item.createDate,
								//在获取数据时为每个卡片添加offsetX属性并初始化为0：
								offsetX: 0,
								// 保留原始数据
								originalData: item
							};
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('获取结算卡列表失败:', error);
					uni.showToast({
						title: '获取列表失败',
						icon: 'none'
					});
				}
			},
			async delClick(name, id) {
				uni.showModal({
					title: '提示',
					content: '确定删除该银行卡吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								uni.showLoading({
									title: '删除中...',
									mask: true
								});

								const result = await deleteSettlement(id);

								uni.hideLoading();

								if (result.code === 200) {
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									});
									// 刷新列表
									setTimeout(() => {
										this.getSettlementList();
									}, 1000);
								}
							} catch (error) {
								uni.hideLoading();
								console.error('删除银行卡失败:', error);
								uni.showToast({
									title: '删除失败，请重试',
									icon: 'none'
								});
							}
						}
					}
				});
			},
			// 编辑银行卡
			editCard(item) {
				console.log(item);
				// 传递银行卡信息到编辑页面
				const cardData = encodeURIComponent(JSON.stringify({
					id: item.id,
					bankName: item.bankName,
					bankNo: item.bankNo,
					accName: item.accName,
					moblie: item.moblie,
					province: item.province,
					city: item.city,
					bankAccountType: item.bankAccountType,
					branchBankName: item.branchBankName,
					idCard: item.idCard
				}));

				uni.navigateTo({
					url: `/subpages/finance/add-card?cardData=${cardData}`
				});
			},

			addCard(){
				uni.navigateTo({
					url: '/subpages/finance/add-card'
				})
			},
			// 触摸开始（记录起始位置和当前卡片索引）
			onTouchStart(e, index) {
				// 关闭其他卡片的删除状态
				this.houseArr.forEach((item, i) => {
					if (i !== index && item.offsetX !== 0) {
						item.offsetX = 0
					}
				})
				this.startX = e.touches[0].clientX
				this.startY = e.touches[0].clientY
				this.currentI = index // 保存当前操作的卡片索引
			},

			// 触摸移动（动态计算偏移量）
			onTouchmove(e, index) {
				if (this.currentI === undefined) return
				// 增加Y轴偏移判断（防误触）
				const deltaY = Math.abs(e.touches[0].clientY - this.startY)
				if (deltaY > 15) return // 纵向滑动超过15px不响应
				const currentX = e.touches[0].clientX
				const deltaX = currentX - this.startX

				// 左滑时限制最大偏移-100px，右滑归零
				this.houseArr[this.currentI].offsetX = Math.max(deltaX, -120)
			},

			// 触摸结束（判断是否显示删除按钮）
			onTouchEnd(e, index) {
				console.log('onTouchEnd', e)
				if (this.currentI === undefined) return
				const offset = this.houseArr[this.currentI].offsetX

				// 滑动超过一半则固定显示删除按钮，否则隐藏
				if (offset < -50) {
					this.houseArr[this.currentI].offsetX = -50
				} else {
					this.houseArr[this.currentI].offsetX = 0
				}
				this.currentI = undefined // 重置索引
			}
		}
	}
</script>

<style scoped lang="scss">
	.withdraw_cash {
		padding: 0 20rpx;
		border-radius: 40rpx;

		.withdraw_log {
			margin-top: 20rpx;
			// padding-left: 20rpx;
			// background-color: #fff;
			border-radius: 20rpx;

			.withdraw_cash_list {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx 30rpx 30rpx 30rpx;

				// border-bottom: 1px solid #F5F5F5;
				.withdraw_cash_left {
					display: flex;
					align-items: center;
					flex: 1;
					position: relative;
				}

				/* 编辑按钮 */
				.edit-btn {
					position: absolute;
					right: 0;
					top: 50%;
					transform: translateY(-50%);
					width: 60rpx;
					height: 60rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: #f8f8f8;
					border-radius: 50%;
					z-index: 10;
				}

				.edit-icon {
					width: 32rpx;
					height: 32rpx;
				}

				.right-icon {
					margin-left: 10rpx;
					width: 14rpx;
					height: 14rpx;
					border-top: 1px solid #666666;
					border-right: 1px solid #666666;
					transform: rotate(45deg);
				}
			}
		}
	}

	/*白色卡片 */
	.list-item {
		background: #ffffff;
		// box-shadow: 0rpx 3rpx 21rpx 0rpx rgba(32, 81, 131, 0.1);
		border-radius: 20rpx;
		// margin: 0 auto;
		// padding: 24rpx 26rpx 18rpx 30rpx;
		box-sizing: border-box;
		position: relative;
		// margin-bottom: 20rpx;
		margin-top: 20rpx;
		display: flex;
		// width: 100%;
		overflow: hidden;
		/* 隐藏删除按钮 */
		transition: transform 0.3s ease;
		/*动画效果 */
	}

	/*白色卡片上面的内容样式 */
	.content {
		flex: 1;
		background: #fff;
		padding: 20rpx;
		transition: transform 0.3s ease;
		z-index: 2;
	}

	/*删除按钮 */
	.delete-btn {
		width: 120rpx;
		height: 100%;
		background: #4D40E5;
		// border-radius: 16rpx 0 0 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		right: -120rpx;
		/* 初始隐藏在右侧 */
		top: 0;
		bottom: 0;
		z-index: 20;
		color: #ffffff;
		/* 层级最高，显示在最上面 */
	}

	/* 调整卡片滑动距离 */
	.list-item[style*='-120px'] {
		transform: translateX(-120rpx);
	}

	/* 当卡片左滑时，删除按钮从右侧滑入 */
	.list-item[style*='-50px'] .delete-btn {
		right: 0;
	}

	.container_money_footer {
		position: fixed;
		bottom: 68rpx;
		left: 0;
		right: 0;
		z-index: 10;
		background-color: #F5F5F5;

		.but {
			color: #ffffff;
			font-size: 30rpx;
			width: 90vw;
			height: 86rpx;
			border-radius: 20rpx;
			margin: 50rpx auto 0 auto;
			background-color: #4D40E5;
			line-height: 86rpx;
		}
	}
</style>