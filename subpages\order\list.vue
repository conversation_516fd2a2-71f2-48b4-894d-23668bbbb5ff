<template>
	<view class="page">
		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<uni-icons type="search" size="16" color="#999999" class="search-icon"></uni-icons>
				<input class="search-input" placeholder="搜索订单" placeholder-style="color: #999999" />
				<view class="search-btn">搜索</view>
			</view>
		</view>

		<!-- 标签页 -->
		<view class="tabs-section">
			<view class="tabs-container">
				<view 
					class="tab-item" 
					:class="{ 'active': currentTab === index }"
					v-for="(tab, index) in tabs" 
					:key="index"
					@click="switchTab(index)"
				>
					<text class="tab-text">{{ tab.name }}</text>
					<image 
						v-if="currentTab === index" 
						class="tab-selected-icon" 
						src="/static/images/icon_option_select.png" 
						mode="aspectFill"
					></image>
				</view>
			</view>
		</view>

		<!-- 订单列表 -->
		<scroll-view
			class="order-list"
			scroll-y="true"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="onRefresh"
			:refresher-triggered="refreshing"
		>
			<view
				class="order-item"
				v-for="(order, index) in filteredOrders"
				:key="order.id || index"
				@click="() => goToOrderDetail(order)"
			>
				<view class="order-header">
					<text class="order-number">{{ order.orderNumber }}</text>
					<text class="order-status">{{ order.statusText }}</text>
				</view>
				
				<view class="order-content">
					<view class="product-section">
						<image class="product-image" :src="order.productImage" mode="aspectFill"></image>
						<view class="product-info">
							<view class="product-title-row">
								<text class="product-name">{{ order.productName }}</text>
								<view class="price-section">
									<text class="price-symbol">¥</text>
									<text class="price-amount">{{ order.actualPrice }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="order-bottom-row">
					<text class="order-time">{{ order.orderTime }}</text>
					<text class="price-label">共{{ order.quantity }}件商品 实付：
						<text class="price-amounttwo">¥{{ order.actualPrice }}</text>
						</text>
				</view>
				
				<view class="order-actions">
					<!-- 已签收：不显示按钮 -->
					<template v-if="order.status === '3'">
						<!-- 不显示任何按钮 -->
					</template>

					<!-- 待收货：显示确认收货按钮 -->
					<template v-else-if="order.status === '2'">
						<view class="action-btn primary" @click.stop="() => confirmReceive(order)">
							确认收货
						</view>
					</template>

					<!-- 待发货：不显示按钮 -->
					<template v-else-if="order.status === '1'">
						<!-- 不显示任何按钮 -->
					</template>

					<!-- 待支付：显示取消订单和立即支付按钮 -->
					<template v-else-if="order.status === '0'">
						<view class="action-btn secondary" @click.stop="() => deleteOrder(order)">
							取消订单
						</view>
						<view class="action-btn primary" @click.stop="() => payOrder(order)">
							立即支付
						</view>
					</template>


				</view>
			</view>

			<!-- 加载更多提示 -->
			<view v-if="loading && orders.length > 0" class="loading-more">
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 没有更多数据提示 -->
			<view v-if="!hasMore && orders.length > 0" class="no-more">
				<text class="no-more-text">没有更多数据了</text>
			</view>

			<!-- 空状态 -->
			<view v-if="!loading && orders.length === 0" class="empty-state">
				<text class="empty-text">暂无订单</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { orderApi } from '@/api/index.js'

export default {
	data() {
		return {
			currentTab: 0,
			tabs: [
				{ name: '全部', value: '4' },
				{ name: '待付款', value: '0' },
				{ name: '待发货', value: '1' },
				{ name: '待收货', value: '2' },
				{ name: '已签收', value: '3' }
			],
			orders: [],
			// 分页参数
			page: 1,
			limit: 10,
			loading: false,
			hasMore: true,
		refreshing: false, // 下拉刷新状态
			// 模拟数据（等待API响应后删除）
			mockOrders: [
				{
					orderNumber: '202507122876540',
					status: 'completed',
					statusText: '已完成',
					productImage: '/static/images/icon_weixin_kind.png',
					productName: '名称名称名称名称名称名称名称名称名称名称名称',
					orderTime: '2025.05.28 11:12:45',
					quantity: 1,
					actualPrice: '19.99'
				},
				{
					orderNumber: '202507122876540',
					status: 'pending',
					statusText: '待支付',
					productImage: '/static/images/icon_car_kind.png',
					productName: '名称名称名称名称名称名称名称名称名称名称名称',
					orderTime: '2025.05.28 11:12:45',
					quantity: 1,
					actualPrice: '19.99'
				},
				{
					orderNumber: '202507122876540',
					status: 'paid',
					statusText: '待发货',
					productImage: '/static/images/icon_weixin_kind.png',
					productName: '名称名称名称名称名称名称名称名称名称名称名称',
					orderTime: '2025.05.28 11:12:45',
					quantity: 1,
					actualPrice: '19.99'
				},
				{
					orderNumber: '202507122876540',
					status: 'shipped',
					statusText: '待收货',
					productImage: '/static/images/icon_car_kind.png',
					productName: '名称名称名称名称名称名称名称名称名称名称名称',
					orderTime: '2025.05.28 11:12:45',
					quantity: 1,
					actualPrice: '19.99'
				},
				{
					orderNumber: '202507122876540',
					status: 'cancelled',
					statusText: '已取消',
					productImage: '/static/images/icon_weixin_kind.png',
					productName: '名称名称名称名称名称名称名称名称名称名称名称',
					orderTime: '2025.05.28 11:12:45',
					quantity: 1,
					actualPrice: '19.99'
				}
			]
		}
	},

	onLoad() {
		this.loadOrderList();
	},

	onShow() {
		// 页面显示时刷新订单列表
		this.refreshOrderList();
	},

	computed: {
		filteredOrders() {
			// 使用真实数据，如果没有数据则显示空列表
			if (this.orders.length > 0) {
				// 真实数据已经通过API参数进行了筛选，直接返回
				return this.orders;
			} else {
				// 如果没有真实数据，显示空列表（不再使用模拟数据）
				return [];
			}
		}
	},
	methods: {
		// 加载订单列表
		async loadOrderList(isRefresh = false) {
			if (this.loading) return;

			try {
				this.loading = true;

				if (isRefresh) {
					this.page = 1;
					this.hasMore = true;
				}

				// 构造请求参数
				const params = {
					page: this.page,
					limit: this.limit,
					orderStatus: this.tabs[this.currentTab].value
				};

				console.log('订单列表请求参数:', params);

				uni.showLoading({
					title: '加载中...',
					mask: true
				});

				const result = await orderApi.getOrderList(params);

				uni.hideLoading();
				this.loading = false;

				if (result.code === 0 || result.code === 200) {
					console.log('订单列表响应数据:', result.data);

					// 映射订单数据
					const orderList = (result.data.records || []).map(item => {
						return {
							id: item.id,
							orderNumber: item.orderNo || `ORDER_${item.id}`, // 提供默认值
							goodsId: item.goodsId,
							status: item.orderStatus,
							statusText: this.getOrderStatusText(item.orderStatus),
							productImage: item.goodsImg,
							productName: item.goodsName,
							specification: item.specification,
							price: item.price,
							quantity: item.goodsNum,
							totalAmount: item.totalAmt,
							actualPrice: item.totalAmt,
							freight: item.freight,
							orderTime: this.formatTime(item.createDate),
							payOrderNo: item.payOrderNo,
							payTime: item.payTime,
							deliveryTime: item.deliveryTime,
							confirmTime: item.confirmTime,
							address: item.address,
							expressCompany: item.expressCompany,
							expressNo: item.expressNo,
							pickStore: item.pickStore,
							remarks: item.remarks
						};
					});

					if (isRefresh) {
						this.orders = orderList;
					} else {
						this.orders = [...this.orders, ...orderList];
					}

					// 更新分页信息
					this.hasMore = result.data.current < result.data.pages;
					if (this.hasMore) {
						this.page++;
					}
				}
			} catch (error) {
				uni.hideLoading();
				this.loading = false;
				console.error('加载订单列表失败:', error);
				uni.showToast({
					title: '加载失败，请重试',
					icon: 'none'
				});
			}
		},

		// 刷新订单列表
		refreshOrderList() {
			this.loadOrderList(true);
		},

		// 获取订单状态文本
		getOrderStatusText(status) {
			if (status === '0') {
				return '待支付';
			} else if (status === '1') {
				return '待发货';
			} else if (status === '2') {
				return '待收货';
			} else if (status === '3') {
				return '已签收';
			} else if (status === '4') {
				return '已取消';
			} else {
				return '未知状态';
			}
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			const date = new Date(timeStr);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
		},

		// 切换标签页
		switchTab(index) {
			this.currentTab = index;
			this.refreshOrderList(); // 切换标签时重新加载数据
		},

		// 下拉刷新
		onRefresh() {
			this.refreshing = true;
			this.refreshOrderList();
			setTimeout(() => {
				this.refreshing = false;
			}, 1000);
		},

		// 上拉加载更多
		loadMore() {
			if (!this.loading && this.hasMore) {
				this.loadOrderList(false);
			}
		},

		// 确认收货
		confirmReceive(order) {
			if (!order || !order.orderNumber) {
				console.error('订单信息不完整:', order);
				uni.showToast({
					title: '订单信息异常',
					icon: 'none'
				});
				return;
			}

			console.log('确认收货:', order);
			uni.showModal({
				title: '确认收货',
				content: '确定已收到商品吗？',
				success: async (res) => {
					if (res.confirm) {
						await this.confirmReceiptRequest(order);
					}
				}
			});
		},

		// 确认收货请求
		async confirmReceiptRequest(order) {
			try {
				uni.showLoading({
					title: '确认中...',
					mask: true
				});

				const result = await orderApi.confirmReceipt(order.orderNumber);

				uni.hideLoading();

				if (result.code === 0 || result.code === 200) {
					uni.showToast({
						title: '确认收货成功',
						icon: 'success'
					});

					// 刷新订单列表
					setTimeout(() => {
						this.refreshOrderList();
					}, 1500);
				} else {
					uni.showToast({
						title: result.msg || '确认收货失败，请重试',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('确认收货失败:', error);
				uni.showToast({
					title: '确认收货失败，请重试',
					icon: 'none'
				});
			}
		},

		// 取消/删除订单
		deleteOrder(order) {
			if (!order || !order.orderNumber) {
				console.error('订单信息不完整:', order);
				uni.showToast({
					title: '订单信息异常',
					icon: 'none'
				});
				return;
			}

			console.log('取消订单:', order);

			// 根据订单状态显示不同的提示
			const isCancel = order.status === '0'; // 待支付状态可以取消
			const title = isCancel ? '取消订单' : '删除订单';
			const content = isCancel ? '确定要取消此订单吗？' : '确定要删除此订单吗？';

			uni.showModal({
				title: title,
				content: content,
				success: async (res) => {
					if (res.confirm) {
						if (isCancel) {
							// 调用取消订单接口
							await this.cancelOrderRequest(order);
						} else {
							// 这里可以添加删除订单的逻辑
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
							// 刷新订单列表
							this.refreshOrderList();
						}
					}
				}
			});
		},

		// 取消订单请求
		async cancelOrderRequest(order) {
			try {
				uni.showLoading({
					title: '取消中...',
					mask: true
				});

				const result = await orderApi.cancelOrder(order.orderNumber);

				uni.hideLoading();

				if (result.code === 0 || result.code === 200) {
					uni.showToast({
						title: '取消成功',
						icon: 'success'
					});

					// 刷新订单列表
					setTimeout(() => {
						this.refreshOrderList();
					}, 1500);
				} else {
					uni.showToast({
						title: result.msg || '取消失败，请重试',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('取消订单失败:', error);
				uni.showToast({
					title: '取消失败，请重试',
					icon: 'none'
				});
			}
		},

		// 支付订单
		payOrder(order) {
			if (!order || !order.orderNumber) {
				console.error('订单信息不完整:', order);
				uni.showToast({
					title: '订单信息异常',
					icon: 'none'
				});
				return;
			}

			console.log('支付订单:', order);
			uni.navigateTo({
				url: `/subpages/payment/pay?orderNumber=${order.orderNumber}&amount=${order.actualPrice}`
			});
		},

		// 再来一单
		reorder(order) {
			console.log('再来一单:', order);
			uni.showToast({
				title: '已添加到购物车',
				icon: 'success'
			});
		},

		// 跳转到订单详情
		goToOrderDetail(order) {
			if (!order || !order.orderNumber) {
				console.error('订单信息不完整:', order);
				uni.showToast({
					title: '订单信息异常',
					icon: 'none'
				});
				return;
			}

			uni.navigateTo({
				url: `/subpages/order/detail?orderNumber=${order.orderNumber}&status=${order.status}`
			});
		}
	}
}
</script>

<style scoped>
.page {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 搜索栏样式 */
.search-section {
	/* background-color: #ffffff; */
	padding: 24rpx 32rpx;
}

.search-box {
	display: flex;
	align-items: center;
	background-color: #ffffff;
	border-radius: 10rpx;
	padding: 16rpx 24rpx;
}

.search-icon {
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
}

.search-btn {
	background-color: #4D40E5;
	color: #ffffff;
	font-size: 26rpx;
	padding: 12rpx 32rpx;
	border-radius: 10rpx;
	margin-left: 16rpx;
}

/* 标签页样式 */
.tabs-section {
	padding: 0;
	/* background-color: #ffffff; */
	/* border-bottom: 1rpx solid #f0f0f0; */
}

.tabs-container {
	display: flex;
	padding: 0 32rpx;
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 24rpx 0;
	position: relative;
}

.tab-text {
	font-size: 32rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.tab-item.active .tab-text {
	color: #333333;
	font-weight: 500;
}

.tab-selected-icon {
	width: 150rpx;
	height: 20rpx;
	position: absolute;
	bottom: 0;
}

/* 订单列表样式 */
.order-list {
	padding: 24rpx 32rpx;
	height: calc(100vh - 200rpx); /* 减去搜索栏和标签页的高度 */
	box-sizing: border-box;
}

.order-item {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	padding: 32rpx;
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
	padding-bottom: 24rpx;
}

.order-number {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

.order-status {
	font-size: 28rpx;
	color: #666666;
}

.order-content {
	margin-bottom: 16rpx;
}

.product-section {
	display: flex;
	margin-bottom: 16rpx;
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	margin-right: 24rpx;
}

.product-info {
	flex: 1;
}

.order-bottom-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25rpx;
}

.product-title-row {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.product-name {
	font-size: 28rpx;
	color: #333333;
	line-height: 40rpx;
	flex: 1;
	margin-right: 20rpx;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.order-time {
	font-size: 24rpx;
	color: #999999;
	flex-shrink: 0;
}

.price-section {
	display: flex;
	align-items: baseline;
	flex-shrink: 0;
}

.price-label {
	font-size: 24rpx;
	color: #999999;
	white-space: nowrap;
}

.price-symbol {
	font-size: 24rpx;
	color: #333333;
}

.price-amount {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

.price-amounttwo {
	font-size: 32rpx;
	color: #FF2222;
	font-weight: 500;
}

.order-actions {
	display: flex;
	justify-content: flex-end;
	gap: 24rpx;
}

.action-btn {
	padding: 16rpx 40rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	text-align: center;
	min-width: 140rpx;
}

.action-btn.primary {
	background-color: #4D40E5;
	color: #ffffff;
}

.action-btn.secondary {
	background-color: #ffffff;
	color: #666666;
	border: 2rpx solid #e0e0e0;
}

.countdown-btn {
	min-width: 200rpx !important;
}

/* 加载状态样式 */
.loading-more {
	padding: 40rpx 0;
	text-align: center;
}

.loading-text {
	color: #999999;
	font-size: 28rpx;
}

.no-more {
	padding: 40rpx 0;
	text-align: center;
}

.no-more-text {
	color: #999999;
	font-size: 28rpx;
}

.empty-state {
	padding: 120rpx 0;
	text-align: center;
}

.empty-text {
	color: #999999;
	font-size: 32rpx;
}
</style>
