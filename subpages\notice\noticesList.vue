<template>
	<view class="notice-list">
		<view class="each-notice" v-for="(item, index) in noticesData" :key="index">
			<view class="left">
				<view class="notice-title">
					<view class="notice-name">{{ item.title }}</view>
					<view class="notice-content">{{ item.content }}</view>
				</view>
				<view class="notice-time">{{ item.createDate }}</view>
			</view>
			<view class="right-icon">
			</view>
		</view>
	</view>
</template>

<script>
import { systemApi } from '@/api/index.js';
	export default {
		name: 'noticesList',
		data() {
			return {
				noticeType: '',
				noticesData: [{
						id: 20250002,
						name: '更新通知',
						time: '2023-12-12 06:45:18',
						content: '我们已经更新了一些功能，欢迎您继续使用！tabbar是原生的，层级高于前端元素，所以会遮挡住前端元素。uni-app插件市场有封装的前端tabbar，但性能不如原生tabbar'
					},
					{
						id: 20250001,
						name: '上线通知',
						time: '2023-09-01 20:32:05',
						content: '今天是我们上线的日子，我们将为您带来更好的服务，请您多多支持！如果想要一个中间带+号的tabbar，在HBuilderX中新建uni-app项目、选择 底部选项卡 模板以上大部分操作 tabbar 的 API 需要在 tabbar 渲染后才能使用，避免在 tabbar 未初始化前使用'
					}
				]
			}
		},
		onLoad(options) {
			this.getNotices()
		},
		methods: {
			// 公告
			async getNotices() {
				try {
					const res = await systemApi.getNotices({
						  "limit": 100,
						  "page": 1,
						  "type": "0"
					});
					console.log('我的账户信息:', res);
			
					// 处理账户数据
					if (res.data) {
						this.noticesData = res.data.total>0?res.data.records:['暂无公告内容']
					}
				} catch (error) {
					console.error('获取账户信息失败:', error);
				}
			},
		}
	}
</script>
<style>
	page {
		background-color: #fafafa;
	}
</style>
<style lang="scss" scoped>
	.notice-list {
		margin: 24rpx;
		border-radius: 20rpx;
		.each-notice {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx;
			border-bottom: 1rpx solid #F7F7F7;
			background-color: #fff;

			.left {
				flex: 1;
				.notice-title {
					flex: 1;
					flex-grow: 1;
					display: flex;
					flex-direction: column;
					font-size: 28rpx;
					margin-bottom: 10rpx;

					.notice-name {
						color: #333;
						margin-right: 20rpx;
					}

					.notice-content {
						color: #a7a7a7;
						font-size: 24rpx;
						margin-top: 10rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
					}
				}

				.notice-time {
					font-size: 24rpx;
					color: #999;
					text-align: right;
				}
			}

			.right {
				width: 40rpx;
			}
		}
	}
</style>