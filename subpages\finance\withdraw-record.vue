<template>
	<view class="page">
		<view class="record-container">
			<view class="record-item" v-for="(item, index) in recordList" :key="index" @click="goToDetail(item)">
				<view class="record-left">
					<image class="record-icon" :src="item.icon" mode="aspectFit"></image>
					<view class="record-info"> 
						<view class="record-title-row">
							<text class="record-title">{{ item.title }}</text>
							<view class="record-status" v-if="item.status !== 'success'">
								<image class="status-bg" :src="item.statusBg" mode="aspectFit"></image>
								<text class="status-text">{{ item.statusText }}</text>
							</view>
						</view>
						<text class="record-time">{{ item.time }}</text>
					</view>
				</view>
				<view class="record-right">
					<text class="record-amount">¥{{ item.amount }}</text>
					<uni-icons type="right" size="16" color="#999999"></uni-icons>
				</view>
			</view>
		</view>

		<view class="empty-state" v-if="recordList.length === 0">
			<text class="empty-text">暂无提现记录</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				recordList: [
					{
						title: '佣金提现至银行卡',
						amount: '327.12',
						time: '2025-06-20 11:12:56',
						status: 'pending',
						statusText: '提现中',
						statusBg: '/static/images/blue.png',
						icon: '/static/images/icon_car_kind.png'
					},
					{
						title: '佣金提现至微信',
						amount: '327.12',
						time: '2025-06-20 11:12:56',
						status: 'failed',
						statusText: '提现失败',
						statusBg: '/static/images/red.png',
						icon: '/static/images/icon_weixin_kind.png'
					},
					{
						title: '佣金提现至银行卡',
						amount: '327.12',
						time: '2025-06-20 11:12:56',
						status: 'success',
						statusText: '',
						statusBg: '',
						icon: '/static/images/icon_car_kind.png'
					},
					{
						title: '佣金提现至微信',
						amount: '327.12',
						time: '2025-06-20 11:12:56',
						status: 'success',
						statusText: '',
						statusBg: '',
						icon: '/static/images/icon_weixin_kind.png'
					},
					{
						title: '佣金提现至支付宝',
						amount: '327.12',
						time: '2025-06-20 11:12:56',
						status: 'success',
						statusText: '',
						statusBg: '',
						icon: '/static/images/icon_car_kind.png'
					}
				]
			}
		},
		onLoad() {
			this.loadRecordData();
		},
		methods: {
			loadRecordData() {
				// 这里可以调用API获取提现记录
			},
			goToDetail(item) {
				// 跳转到提现明细页面
				uni.navigateTo({
					url: `/subpages/finance/withdraw-detail?status=${item.status}&submitTime=${encodeURIComponent(item.time)}`
				});
			}
		}
	}
</script>

<style scoped>
	.page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 24rpx;
	}

	.record-container {
		background-color: #ffffff;
		border-radius: 24rpx;
		overflow: hidden;
	}

	.record-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx 24rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.record-item:last-child {
		border-bottom: none;
	}

	.record-left {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.record-icon {
		width: 48rpx;
		height: 48rpx;
		margin-right: 24rpx;
	}

	.record-info {
		flex: 1;
	}

	.record-title-row {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.record-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
		margin-right: 16rpx;
	}

	.record-status {
		position: relative;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		height: 32rpx;
		padding: 0 16rpx;
		border-radius: 16rpx;
	}

	.status-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}

	.status-text {
		position: relative;
		z-index: 1;
		font-size: 22rpx;
		color: #ffffff;
		font-weight: 500;
	}

	.record-time {
		font-size: 26rpx;
		color: #999999;
	}

	.record-right {
		display: flex;
		align-items: center;
	}

	.record-amount {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
		margin-right: 16rpx;
	}

	.arrow-icon {
		width: 24rpx;
		height: 24rpx;
	}

	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 400rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
</style>
