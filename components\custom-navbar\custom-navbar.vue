<template>
	<view class="custom-navbar" :style="navbarStyle">
		<!-- 状态栏占位 -->
		<view :style="{ height: statusBarHeight + 'px' }"></view>
		<!-- 导航栏内容区 -->
		<view class="navbar-content">
			<view class="navbar-left" @click="handleBack" v-if="showBack">
				<uni-icons type="left" size="20" :color="iconColor"></uni-icons>
			</view>
			<view class="navbar-left" v-else></view>
			
			<text class="page-title" :style="{ color: titleColor }">{{ title }}</text>
			
			<view class="navbar-right">
				<slot name="right"></slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'CustomNavbar',
		props: {
			// 导航栏标题
			title: {
				type: String,
				default: ''
			},
			// 是否显示返回按钮
			showBack: {
				type: Boolean,
				default: true
			},
			// 标题颜色
			titleColor: {
				type: String,
				default: '#000000'
			},
			// 图标颜色
			iconColor: {
				type: String,
				default: '#000000'
			},
			// 背景色
			backgroundColor: {
				type: String,
				default: 'transparent'
			}
		},
		
		data() {
			return {
				statusBarHeight: 20,
				navbarHeight: 80
			}
		},
		
		computed: {
			navbarStyle() {
				return {
					height: this.navbarHeight + 'px',
					paddingTop: this.statusBarHeight + 'px',
					backgroundColor: this.backgroundColor
				}
			}
		},
		
		mounted() {
			this.setNavbarInfo();
		},
		
		methods: {
			// 设置导航栏信息
			setNavbarInfo() {
				try {
					const systemInfo = uni.getSystemInfoSync();
					this.statusBarHeight = systemInfo.statusBarHeight;

					const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
					const menuTop = menuButtonInfo.top;
					const menuHeight = menuButtonInfo.height;

					const contentHeight = menuHeight + (menuTop - this.statusBarHeight) * 2;
					this.navbarHeight = this.statusBarHeight + contentHeight;
				} catch (e) {
					console.error('获取系统信息失败:', e);
					this.statusBarHeight = 20;
					this.navbarHeight = 60;
				}
			},

			// 处理返回事件
			handleBack() {
				this.$emit('back');
				// 如果没有监听back事件，则执行默认返回操作
				if (!this.$listeners.back) {
					uni.navigateBack();
				}
			}
		}
	}
</script>

<style scoped>
	/* 自定义导航栏样式 */
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 999;
		display: flex;
		flex-direction: column;
	}

	.navbar-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 44px;
		padding: 0 30rpx;
	}

	.navbar-left {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		flex: 1;
		text-align: center;
	}

	.navbar-right {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
