// 平台首页相关接口
import request from "@/utils/request.js";

/**
 * 首页数据
 */
export function getFitmentInfoApi() {
	return request.get('fitment/info')
}

/**
 * 首页商户信息分页列表
 * @param {*} data 
 */
export function getMerchantHomePageApi(data) {
	return request.get('merchant/get/home/<USER>', data)
}

/**
 * 首页门店信息分页列表
 * @param {*} data 
 */
export function getStoresHomePageApi(data) {
	return request.get('stores/get/home/<USER>', data)
}

/**
 * 获取商户首页装修信息
 * @param {*} id 
 */
export function getMerchantFitmentApi(data) {
	return request.get(`merchant/get/fitment`, data);
}

/**
 * 关注门店
 * @param {*} id 
 */
export function addCollectMerchantApi(id) {
	return request.post(`collect/add/merchant/${id}`, {});
}

/**
 * 取消关注门店
 * @param {*} id 
 */
export function cancelCollectMerchantApi(id) {
	return request.post(`collect/cancel/merchant/${id}`, {});
}

/**
 * 领券
 * @param {*} id 
 */
export function receiveCouponApi(id) {
	return request.post(`coupon/receive/${id}`, {});
}

/**
 * 获取店铺详情
 * @param {*} id 
 */
export function getMerchantDetailApi(id) {
	return request.get(`merchant/detail/${id}`, {});
}

/**
 * 获取搜索热词列表
 * @param {*} id 
 */
export function getProductHotListApi(data) {
	return request.get('product/hot/words/list', data);
}

/**
 * 获取商品分页列表
 * @param {*} id 
 */
export function getProductListApi(data) {
	return request.post(`/app/goods/list/0`, data);
}

/**
 * 商户分类轮播图查询
 * @param {*} id 
 */
export function getCategoryCarouselApi(data) {
	return request.get('merchant/get/category/carousel', data);
}