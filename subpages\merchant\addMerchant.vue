<template>
	<view class="page">
		<view class="audit_top" v-if="valiFormData.id">
			<view style="display: flex;align-items: center;">
				<view style="display: flex;"><img style="width: 40rpx;height: 40rpx;" src="/static/images/icon_lose_result.png" alt="" /></view>
				<view style="margin-left: 20rpx;font-size: 30rpx;font-weight: 600;">审核失败</view>
			</view>
			<view style="color: #666666;font-size: 24rpx;margin: 10rpx 0 0 60rpx;">失败原因：{{valiFormData.denialReason||'-'}}</view>
		</view>
		<uni-forms ref="valiForm" :rules="rules" :border="true" :modelValue="valiFormData">
			<uni-section titleFontSize="32rpx" title="商户信息" type="line">
				<!-- 基础表单校验 -->
				<view class="example">
					<uni-forms-item label="商户名称" required name="name">
						<uni-easyinput type="text" v-model="valiFormData.name" :clearable="false" :inputBorder="false" placeholder="请输入商户名称" />
					</uni-forms-item>
					<uni-forms-item label="商户姓名" required name="realName">
						<uni-easyinput type="text" v-model="valiFormData.realName" :clearable="false" :inputBorder="false" placeholder="请输入商户姓名" />
					</uni-forms-item>
					<uni-forms-item label="商户手机号" required name="phone">
						<uni-easyinput type="number" v-model="valiFormData.phone" :clearable="false" :inputBorder="false" placeholder="请输入商户手机号" />
					</uni-forms-item>
				</view>
				<view class="example" style="margin-top: 20rpx;">
					<view class="uni-custom">
						<uni-forms-item label="商户分类" required name="categoryId">
							<picker @change="categoryChange" :value="categoryIndex" :range="categoryDict" range-key="name">
								<view style="display: flex;align-items: center;padding-right: 10rpx;">
									<view class="uni-input" :style="{color: valiFormData.categoryId?'':'#999999',paddingRight: '20rpx',fontSize: valiFormData.categoryId?'28rpx':'24rpx'}">{{valiFormData.categoryName?valiFormData.categoryName:'请选择商户分类'}}</view>
									<input hidden="true" type="text" v-model="valiFormData.categoryId" />
									<view class="right-icon"></view>
								</view>
							</picker>
						</uni-forms-item>
					</view>
					<uni-forms-item label="店铺类型" required name="typeId">
						<picker @change="typeChange" :value="typeIndex" :range="typeDict" range-key="name">
							<view style="display: flex;align-items: center;padding-right: 10rpx;">
								<view class="uni-input" :style="{color: valiFormData.typeId?'':'#999999',paddingRight: '20rpx',fontSize: valiFormData.typeId?'28rpx':'24rpx'}">{{valiFormData.typeName?valiFormData.typeName:'请选择店铺类型'}}</view>
								<input hidden="true" type="text" v-model="valiFormData.typeId" />
								<view class="right-icon"></view>
							</view>
						</picker>
					</uni-forms-item>
					<uni-forms-item label="合作折扣(%)" required name="discount">
						<uni-easyinput type="digit" v-model="valiFormData.discount" :clearable="false" :inputBorder="false" placeholder="请输入合作折扣(%)" />
					</uni-forms-item>
					<uni-forms-item label="手续费(%)" required name="handlingFee">
						<uni-easyinput type="digit" v-model="valiFormData.handlingFee" :clearable="false" :inputBorder="false" placeholder="请输入手续费(%)" />
					</uni-forms-item>
				</view>
				<view class="example uni-custom" style="margin-top: 20rpx;">
					<uni-forms-item label="商户关键字" required name="keywords">
						<uni-easyinput type="text" v-model="valiFormData.keywords" :clearable="false" :inputBorder="false" placeholder="请输入商户关键字" />
					</uni-forms-item>
				</view>
			</uni-section>
			<uni-section titleFontSize="32rpx" title="上传凭证" type="line">
				<view class="example">
					<view class="uni-forms-item" style="padding-top: 20rpx;">
						<view class="uni-forms-item__label"><text class="is-required">*</text><text>上传资质图片</text></view>
						<view style="margin-top: 30rpx;">
							<uni-grid :column="4" :show-border="false" :highlight="false">
								<uni-grid-item v-for="(item, index) in valiFormData.qualificationPicture" :index="index" :key="index">
									<view class="grid-item-box" style="background-color: #fff;">
										<img style="width: 120rpx;height: 120rpx;" :src="item" alt="" />
										<img @click="delImages(item,'qualificationPicture')" class="del_round" src="/static/images/close_icon.png" alt="" />
									</view>
								</uni-grid-item>
								<uni-grid-item v-if="valiFormData.qualificationPicture.length<1">
									<view class="grid-item-box">
										<img @click="picUP('qualificationPicture')" style="width: 120rpx;height: 120rpx;" src="/static/images/img_ phone.png" alt="" />
									</view>
								</uni-grid-item>
							</uni-grid>
						</view>
					</view>
					<view class="uni-forms-item" style="border-top: 1px #eee solid;padding-top: 20rpx;">
						<view class="uni-forms-item__label"><text class="is-required">*</text><text>上传合同</text></view>
						<view style="margin-top: 30rpx;">
							<uni-grid :column="4" :show-border="false" :highlight="false">
								<uni-grid-item v-for="(item, index) in valiFormData.contractPicture" :index="index" :key="index">
									<view class="grid-item-box" style="background-color: #fff;">
										<img style="width: 120rpx;height: 120rpx;" :src="item" alt="" />
										<img @click="delImages(item,'contractPicture')" class="del_round" src="/static/images/close_icon.png" alt="" />
									</view>
								</uni-grid-item>
								<uni-grid-item v-if="valiFormData.qualificationPicture.length<9">
									<view class="grid-item-box">
										<img @click="picUP('contractPicture')" style="width: 120rpx;height: 120rpx;" src="/static/images/img_ phone.png" alt="" />
									</view>
								</uni-grid-item>
							</uni-grid>
						</view>
					</view>
				</view>
				<view class="example" style="margin-top: 20rpx;">
					<view class="uni-custom">
						<uni-forms-item label="是否推荐" name="isRecommend">
							<uni-data-checkbox v-model="valiFormData.isRecommend" :localdata="yesOrNo" />
						</uni-forms-item>
					</view>
					<uni-forms-item label="是否自营" name="isSelf">
						<uni-data-checkbox v-model="valiFormData.isSelf" :localdata="yesOrNo" />
					</uni-forms-item>
					<uni-forms-item label="商品是否审核" name="productSwitch">
						<uni-data-checkbox v-model="valiFormData.productSwitch" :localdata="yesOrNo" />
					</uni-forms-item>
				</view>	
			</uni-section>
			<view class="footer">
				<button type="primary" @click="submit('valiForm')">提交</button>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import { distributionApi, userApi } from '@/api/index.js';
	export default {
		name: 'orderList',
		data() {
			return {
				// 校验表单数据
				valiFormData: {
				  categoryId: '',
				  discount: null,
				  handlingFee: null,
				  isRecommend: 0,
				  isSelf: 0,
				  productSwitch: 1,
				  keywords: '',
				  name: '',
				  phone: null,
				  qualificationPicture: [],
				  contractPicture: [],
				  realName: '',
				  typeId: null
				},
				// 单选数据源
				yesOrNo: [{
					text: '是',
					value: 1
				},{
					text: '否',
					value: 0
				}],
				// 校验规则
				rules: {
					name: {
						rules: [{
							required: true,
							errorMessage: '请输入商户名称'
						}]
					},
					realName: {
						rules: [{
							required: true,
							errorMessage: '请输入商户姓名'
						}]
					},
					phone: {
						rules: [{
							required: true,
							errorMessage: '请输入商户手机号'
						}]
					},
					categoryId: {
						rules: [{
							required: true,
							errorMessage: '请选择商户分类'
						}]
					},
					typeId: {
						rules: [{
							required: true,
							errorMessage: '请选择店铺类型'
						}]
					},
					discount: {
						rules: [{
							required: true,
							errorMessage: '请输入合作折扣(%)'
						}]
					},
					handlingFee: {
						rules: [{
							required: true,
							errorMessage: '请输入手续费(%)'
						}]
					},
					keywords: {
						rules: [{
							required: true,
							errorMessage: '请输入商户关键字'
						}]
					},
					isRecommend: {
						rules: [{
							required: true,
							errorMessage: '请选择是否推荐'
						}]
					},
					isSelf: {
						rules: [{
							required: true,
							errorMessage: '请选择是否自营'
						}]
					},
					productSwitch: {
						rules: [{
							required: true,
							errorMessage: '请选择商品是否审核'
						}]
					},
				},
				categoryDict: [],
				typeDict: [],
				categoryIndex: 0,
				typeIndex: 0
			}
		},
		onShow: function() {
			// this.orderList=[]
			// this.orderParams.page = 1
			// this.getOrderData()
		},
		async onLoad(options) {
			await this.getMerchantCategory()
			await this.getMerchantType()
			if(options.params) {
				const params = JSON.parse(options.params)
				const qualificationPicture = params.qualificationPicture.split(',')
				params.qualificationPicture = qualificationPicture.filter((_, index) => index === 0)
				params.contractPicture = qualificationPicture.slice(1)
				params.categoryName = this.categoryDict.filter(item=>item.id==params.categoryId)[0].name
				// params.typeName = this.categoryDict.filter(item=>item.id==params.typeId)[0].name
				this.valiFormData = {...params}
				console.log(this.valiFormData)
			}
		},
		methods: {
			// 分类
			async getMerchantCategory() {
				const res = await distributionApi.getMerchantCategory()
				this.categoryDict = res.data
			},
			// 类型
			async getMerchantType() {
				const res = await distributionApi.getMerchantType()
				this.typeDict = res.data||this.categoryDict
			},
			categoryChange(e) {
				this.categoryIndex = e.detail.value
				this.valiFormData.categoryId = this.categoryDict[this.categoryIndex].id
				this.valiFormData.categoryName = this.categoryDict[this.categoryIndex].name
			},
			typeChange: function(e) {
				this.typeIndex = e.detail.value
				this.valiFormData.typeId = this.typeDict[this.typeIndex].id
				this.valiFormData.typeName = this.typeDict[this.typeIndex].name
			},
			// 获取表单数据
			async submit(ref) {
				this.$refs[ref].validate().then(res => {
					console.log('success', res);
					let qualificationPicture = this.valiFormData.qualificationPicture.concat(this.valiFormData.contractPicture)
					res.qualificationPicture = qualificationPicture.length?qualificationPicture.join(','):''
					res.discount = +this.valiFormData.discount
					res.handlingFee = +this.valiFormData.handlingFee
					res.id = this.valiFormData.id
					if(this.valiFormData.qualificationPicture.length==0) {
						uni.showToast({
							title: '请上传资质照片',
							icon: 'none'
						})
						return
					}
					if(qualificationPicture.length==1) {
						uni.showToast({
							title: '请上传合同',
							icon: 'none'
						})
						return
					}
					this.addAgent(res)
				}).catch(err => {
					console.log('err', err);
				})
			},
			// 新增表单
			addAgent(params) {
				distributionApi[this.valiFormData.id?'editMerchant':'addMerchant']({...params}).then((res) => {
					console.log(res)
					if (res.code !== 0) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					} else {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						})
						setTimeout(() => {
							// 进列表
							uni.navigateBack()
						}, 1000)
					}
				})
			},
			// 上传图片
			picUP: function(name) {
				var that = this
				uni.chooseImage({
					count: 1, //图片可选择数量
					sizeType: ['compressed'], //original 原图，compressed 压缩图，默认二者都有
					sourceType: ['album', 'camera'], //album 从相册选图，camera 使用相机，默认二者都有。
					extension: ['.png', '.jpg'], // 限制可选择的图片格式
					success: res => {
						that.uploadImg(res.tempFilePaths[0],name);
					},
					fail: res => {
					},
				});
			},
			async uploadImg(file,name) {
				try {
					const res = await userApi.uploadFile(file);
					if (res.data) {
						uni.showToast({
							title: '上传成功！'
						});
						if(name=='frontUrl'||name=='contraryUrl'){
							this.valiFormData[name] = res.data.url;
						}else{
							this.valiFormData[name].push(res.data.url)
						}
					}
				} catch (error) {
					console.error('获取失败:', error);
				}
			},
			delImages(item,name) {
				this.valiFormData[name] = this.valiFormData[name].filter(el=>el!==item)
			}
		}
	}
</script>


<style lang="scss">
	page{
		background-color: #F7F7F7;
	}
</style>
<style lang="scss" scoped>
	.page{
		padding: 30rpx;
		padding-bottom: 200rpx;
		.example{
			border-radius: 20rpx;
			background-color: white;
			padding: 0 24rpx;
		}
	}
	.audit_top{
		padding: 20rpx 24rpx;
		border-radius: 20rpx;
		background-image: linear-gradient(to right, #E8E8F6, #DFF2F6);
	}
	/deep/.uni-section{
		background-color: #F7F7F7;
	}
	/deep/.uni-section-header{
		padding: 24rpx 0 !important;
	}
	/deep/ .uni-section-header__decoration line{
		background-color: #7675FE;
	}
	/deep/ .uni-section .uni-section-header__content .distraction {
		font-weight: 600;
	}
	/deep/.uni-forms-item {
		justify-content: space-between;
		align-items: center;
	}
	/deep/.uni-forms-item__content {
		flex: none;
	}
	/deep/.uni-easyinput__content-input {
		text-align: right;
	}
	/deep/.uni-forms-item__label{
		width: auto !important;
		font-size: 28rpx !important;
		color: #333333;
		// font-weight: 600;
	}
	.uni-custom{
		/deep/ .uni-forms-item--border {
			border: none;
		}
	}
	.uni-forms-item__label{
		font-size: 28rpx;
		color: #333333;
	}
	.uni-forms-item .is-required {
	    color: #dd524d;
	    font-weight: bold;
	}
	.grid-item-box{
		position: relative;
		display: flex;
	}
	.del_round{
		width: 30rpx;
		height: 30rpx;
		position: absolute;
		right: 42rpx;
		top: 0;
		z-index: 5;
		background-color: #FFFFFF;
		border-radius: 50%;
	}
	.footer{
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 12rpx 30rpx;
		padding-bottom: 68rpx;
		z-index: 5;
		background-color: #F7F7F7;
		button{
			color: white;
			font-size: 32rpx;
			padding: 22rpx 0;
			background-color: #7675FE;
		}
	}
</style>