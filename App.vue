<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
@import "static/css/base.css";
.text-ellipsis_1{
   overflow : hidden;
   text-overflow: ellipsis;
   display: -webkit-box;
   -webkit-line-clamp: 1;
   -webkit-box-orient: vertical;
   word-break: break-all;
}
.text-ellipsis_2{
   overflow : hidden;
   text-overflow: ellipsis;
   display: -webkit-box;
   -webkit-line-clamp: 2;
   -webkit-box-orient: vertical;
   word-break: break-all;
}
.right-icon{
	width: 14rpx;
	height: 14rpx;
	border-top: 1px solid #666666;
	border-right: 1px solid #666666;
	transform: rotate(45deg);
}
</style>


