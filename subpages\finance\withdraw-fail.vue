<template>
	<view class="page">

		<!-- 失败图标和内容 -->
		<view class="fail-content">
			<view class="fail-icon">
				<image class="fail-img" src="/static/images/img_lose_default.png" mode="aspectFit"></image>
			</view>

			<view class="fail-text">
				<view class="main-title">提现申请失败</view>
				<view class="sub-title">提现¥{{ withdrawAmount }}至余额申请失败，请重新申请</view>
			</view>

			<!-- 按钮区域 -->
			<view class="btn-section">
				<button class="cancel-btn" @click="goToHome">不提现了，去逛逛</button>
				<button class="retry-btn" @click="retryWithdraw">返回重新提现</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			withdrawAmount: '10.00', // 提现金额
			failReason: '' // 失败原因
		}
	},
	onLoad(options) {
		// 接收从提现页面传递过来的参数
		if (options.amount) {
			this.withdrawAmount = options.amount;
		}
		if (options.reason) {
			this.failReason = decodeURIComponent(options.reason);
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 重新提现
		retryWithdraw() {
			// 返回到提现页面
			uni.navigateBack();
		},

		// 跳转到首页
		goToHome() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		}
	}
}
</script>

<style scoped>
.page {
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

/* 头部 */
.header {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 30rpx;
	background-color: #ffffff;
}

.back-btn {
	position: absolute;
	left: 30rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
}

/* 失败内容区域 */
.fail-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 0 60rpx;
	margin-top: -100rpx; /* 向上偏移一些，让内容更居中 */
}

.fail-icon {
	margin-bottom: 60rpx;
}

.fail-img {
	width: 200rpx;
	height: 200rpx;
}

.fail-text {
	text-align: center;
	margin-bottom: 80rpx;
}

.main-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 30rpx;
}

.sub-title {
	font-size: 28rpx;
	color: #999999;
	line-height: 1.6;
	padding: 0 20rpx;
}

/* 按钮区域 */
.btn-section {
	width: 100%;
	padding: 0 40rpx;
	display: flex;
	flex-direction: row;
	gap: 20rpx;
}

.cancel-btn {
	flex: 1;
	height: 88rpx;
	background-color: #f5f5f5;
	border-radius: 20rpx;
	border: 2rpx solid #999999;
	color: #666666;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.retry-btn {
	flex: 1;
	height: 88rpx;
	background-color: #4D40E5;
	border-radius: 20rpx;
	border: none;
	color: #ffffff;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.cancel-btn::after,
.retry-btn::after {
	border: none;
}
</style>
