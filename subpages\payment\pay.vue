<template>
	<view class="payment-page">
		<!-- 支付金额区域 -->
		<view class="amount-section">
			<view class="amount-wrapper">
				<text class="currency-symbol">¥</text>
				<text class="amount-value">{{ paymentAmount }}</text>
			</view>
			<view class="countdown-wrapper">
				<text class="countdown-text">剩余支付时间 {{ countdownTime }}</text>
			</view>
		</view>
		
		<!-- 支付方式区域 -->
		<view class="payment-method-section">
			<view class="payment-method-item" @click="selectPaymentMethod('wechat')">
				<view class="method-left">
					<image src="/static/images/icon_weixin_pay.png" class="payment-icon"></image>
					<text class="method-name">微信支付</text>
				</view>
				<view class="method-right">
					<view class="radio-btn" :class="{ 'selected': selectedMethod === 'wechat' }">
						<view class="radio-inner" v-if="selectedMethod === 'wechat'"></view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部确认支付按钮 -->
		<view class="bottom-pay-section">
			<view class="confirm-pay-btn" @click="confirmPayment">
				<text class="pay-btn-text">确认支付</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PaymentPage',
	data() {
		return {
			paymentAmount: '127.99',
			selectedMethod: 'wechat',
			countdownTime: '14:12',
			countdownTimer: null
		}
	},
	onLoad(options) {
		// 获取支付金额
		if (options.amount) {
			this.paymentAmount = options.amount;
		}
		// 开始倒计时
		this.startCountdown();
	},
	onUnload() {
		// 页面卸载时清除定时器
		if (this.countdownTimer) {
			clearInterval(this.countdownTimer);
		}
	},
	methods: {
		// 选择支付方式
		selectPaymentMethod(method) {
			this.selectedMethod = method;
		},
		
		// 确认支付
		confirmPayment() {
			if (this.selectedMethod === 'wechat') {
				this.wechatPay();
			}
		},
		
		// 微信支付
		wechatPay() {
			uni.showLoading({
				title: '正在支付...'
			});

			// 模拟支付过程
			setTimeout(() => {
				uni.hideLoading();

				// 模拟支付结果（90%成功率）
				const isSuccess = Math.random() < 0.1;

				// 跳转到支付结果页
				uni.redirectTo({
					url: `/subpages/payment/result?status=${isSuccess ? 'success' : 'fail'}&amount=${this.paymentAmount}`
				});
			}, 2000);
		},
		
		// 开始倒计时
		startCountdown() {
			let minutes = 14;
			let seconds = 12;
			
			this.countdownTimer = setInterval(() => {
				if (seconds > 0) {
					seconds--;
				} else if (minutes > 0) {
					minutes--;
					seconds = 59;
				} else {
					// 倒计时结束
					clearInterval(this.countdownTimer);
					this.handleTimeout();
					return;
				}
				
				this.countdownTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
			}, 1000);
		},
		
		// 支付超时处理
		handleTimeout() {
			uni.showModal({
				title: '支付超时',
				content: '支付时间已超时，请重新下单',
				showCancel: false,
				success: () => {
					uni.navigateBack();
				}
			});
		}
	}
}
</script>

<style scoped>
.payment-page {
	background-color: #F5F5F5;
	min-height: 100vh;
	padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 支付金额区域 */
.amount-section {
	background-color: #F5F5F5;
	padding: 80rpx 40rpx 60rpx;
	text-align: center;
}

.amount-wrapper {
	display: flex;
	align-items: baseline;
	justify-content: center;
	margin-bottom: 30rpx;
}

.currency-symbol {
	color: #FF2222;
	font-size: 48rpx;
	font-weight: bold;
	margin-right: 10rpx;
}

.amount-value {
	color: #FF2222;
	font-size: 80rpx;
	font-weight: bold;
}

.countdown-wrapper {
	margin-top: 20rpx;
}

.countdown-text {
	color: #999999;
	font-size: 28rpx;
}

/* 支付方式区域 */
.payment-method-section {
	background-color: #ffffff;
	border-radius: 20rpx;
	margin: 40rpx 30rpx;
	padding: 0;
	overflow: hidden;
}

.payment-method-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 30rpx;
	border-bottom: 1rpx solid #F5F5F5;
}

.payment-method-item:last-child {
	border-bottom: none;
}

.method-left {
	display: flex;
	align-items: center;
}

.payment-icon {
	width: 60rpx;
	height: 60rpx;
	margin-right: 20rpx;
}

.method-name {
	color: #333333;
	font-size: 32rpx;
}

.method-right {
	display: flex;
	align-items: center;
}

/* 单选按钮样式 */
.radio-btn {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #CCCCCC;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.radio-btn.selected {
	border-color: #4D40E5;
}

.radio-inner {
	width: 24rpx;
	height: 24rpx;
	background-color: #4D40E5;
	border-radius: 50%;
}

/* 底部支付按钮 */
.bottom-pay-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	padding: 30rpx;
	padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.confirm-pay-btn {
	background-color: #4D40E5;
	border-radius: 20rpx;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.confirm-pay-btn:active {
	background-color: #3d32d1;
}

.pay-btn-text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: bold;
}
</style>
