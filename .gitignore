# uni-app项目忽略文件

# 编译输出目录
/unpackage/dist/
/unpackage/build/

# 依赖目录
node_modules/

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 编辑器目录和文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 微信小程序工具生成的文件
project.config.json
project.private.config.json

# HBuilderX生成的文件
.hbuilderx/
