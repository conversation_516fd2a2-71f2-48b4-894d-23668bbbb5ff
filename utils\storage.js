/**
 * 本地存储封装
 */

// 存储前缀，避免与其他应用冲突
const STORAGE_PREFIX = 'distribution_app_';

// 默认过期时间（7天）
const DEFAULT_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000;

/**
 * 生成存储key
 * @param {string} key 原始key
 * @returns {string} 带前缀的key
 */
const getStorageKey = (key) => {
	return STORAGE_PREFIX + key;
};

/**
 * 设置存储数据
 * @param {string} key 存储key
 * @param {any} value 存储值
 * @param {number} expireTime 过期时间（毫秒），默认7天
 */
export const setStorage = (key, value, expireTime = DEFAULT_EXPIRE_TIME) => {
	try {
		const data = {
			value,
			timestamp: Date.now(),
			expireTime
		};
		uni.setStorageSync(getStorageKey(key), JSON.stringify(data));
		return true;
	} catch (error) {
		console.error('setStorage error:', error);
		return false;
	}
};

/**
 * 获取存储数据
 * @param {string} key 存储key
 * @param {any} defaultValue 默认值
 * @returns {any} 存储的值或默认值
 */
export const getStorage = (key, defaultValue = null) => {
	try {
		const dataStr = uni.getStorageSync(getStorageKey(key));
		if (!dataStr) {
			return defaultValue;
		}
		
		const data = JSON.parse(dataStr);
		const now = Date.now();
		
		// 检查是否过期
		if (data.expireTime && (now - data.timestamp) > data.expireTime) {
			removeStorage(key);
			return defaultValue;
		}
		
		return data.value;
	} catch (error) {
		console.error('getStorage error:', error);
		return defaultValue;
	}
};

/**
 * 删除存储数据
 * @param {string} key 存储key
 */
export const removeStorage = (key) => {
	try {
		uni.removeStorageSync(getStorageKey(key));
		return true;
	} catch (error) {
		console.error('removeStorage error:', error);
		return false;
	}
};

/**
 * 清空所有存储数据
 */
export const clearStorage = () => {
	try {
		const info = uni.getStorageInfoSync();
		const keys = info.keys;
		
		keys.forEach(key => {
			if (key.startsWith(STORAGE_PREFIX)) {
				uni.removeStorageSync(key);
			}
		});
		return true;
	} catch (error) {
		console.error('clearStorage error:', error);
		return false;
	}
};

/**
 * 获取存储信息
 */
export const getStorageInfo = () => {
	try {
		const info = uni.getStorageInfoSync();
		const appKeys = info.keys.filter(key => key.startsWith(STORAGE_PREFIX));
		
		return {
			keys: appKeys,
			currentSize: info.currentSize,
			limitSize: info.limitSize
		};
	} catch (error) {
		console.error('getStorageInfo error:', error);
		return null;
	}
};

// 常用存储key常量
export const STORAGE_KEYS = {
	TOKEN: 'token',
	USER_INFO: 'user_info',
	SETTINGS: 'settings',
	CART: 'cart',
	SEARCH_HISTORY: 'search_history',
	BROWSE_HISTORY: 'browse_history'
};

// 用户相关存储方法
export const userStorage = {
	// 设置用户token
	setToken(token) {
		return setStorage(STORAGE_KEYS.TOKEN, token, 30 * 24 * 60 * 60 * 1000); // 30天
	},
	
	// 获取用户token
	getToken() {
		return getStorage(STORAGE_KEYS.TOKEN);
	},
	
	// 删除用户token
	removeToken() {
		return removeStorage(STORAGE_KEYS.TOKEN);
	},
	
	// 设置用户信息
	setUserInfo(userInfo) {
		return setStorage(STORAGE_KEYS.USER_INFO, userInfo);
	},
	
	// 获取用户信息
	getUserInfo() {
		return getStorage(STORAGE_KEYS.USER_INFO);
	},
	
	// 删除用户信息
	removeUserInfo() {
		return removeStorage(STORAGE_KEYS.USER_INFO);
	},
	
	// 清除用户相关数据
	clearUserData() {
		removeStorage(STORAGE_KEYS.TOKEN);
		removeStorage(STORAGE_KEYS.USER_INFO);
		removeStorage(STORAGE_KEYS.CART);
	}
};

// 应用设置存储方法
export const settingsStorage = {
	// 设置应用设置
	setSettings(settings) {
		return setStorage(STORAGE_KEYS.SETTINGS, settings);
	},
	
	// 获取应用设置
	getSettings() {
		return getStorage(STORAGE_KEYS.SETTINGS, {
			theme: 'light',
			language: 'zh-CN',
			notifications: true
		});
	},
	
	// 更新单个设置项
	updateSetting(key, value) {
		const settings = this.getSettings();
		settings[key] = value;
		return this.setSettings(settings);
	}
};

// 购物车存储方法
export const cartStorage = {
	// 设置购物车数据
	setCart(cart) {
		return setStorage(STORAGE_KEYS.CART, cart);
	},
	
	// 获取购物车数据
	getCart() {
		return getStorage(STORAGE_KEYS.CART, []);
	},
	
	// 添加商品到购物车
	addToCart(product) {
		const cart = this.getCart();
		const existIndex = cart.findIndex(item => item.id === product.id);
		
		if (existIndex > -1) {
			cart[existIndex].quantity += product.quantity || 1;
		} else {
			cart.push({
				...product,
				quantity: product.quantity || 1
			});
		}
		
		return this.setCart(cart);
	},
	
	// 从购物车移除商品
	removeFromCart(productId) {
		const cart = this.getCart();
		const newCart = cart.filter(item => item.id !== productId);
		return this.setCart(newCart);
	},
	
	// 清空购物车
	clearCart() {
		return this.setCart([]);
	}
};

export default {
	setStorage,
	getStorage,
	removeStorage,
	clearStorage,
	getStorageInfo,
	STORAGE_KEYS,
	userStorage,
	settingsStorage,
	cartStorage
};
