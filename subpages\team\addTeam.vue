<template>
	<view class="page">
		<view class="audit_top" v-if="valiFormData.id">
			<view style="display: flex;align-items: center;">
				<view style="display: flex;"><img style="width: 40rpx;height: 40rpx;" src="/static/images/icon_lose_result.png" alt="" /></view>
				<view style="margin-left: 20rpx;font-size: 30rpx;font-weight: 600;">审核失败</view>
			</view>
			<view style="color: #666666;font-size: 24rpx;margin: 10rpx 0 0 60rpx;">失败原因：{{valiFormData.denialReason||'-'}}</view>
		</view>
		<!-- 基础表单校验 -->
		<uni-forms ref="valiForm" :rules="rules" :border="true" :modelValue="valiFormData">
			<view class="example" style="padding-bottom: 20rpx;">
				<uni-forms-item label="团队名称" required name="teamName">
					<uni-easyinput type="text" v-model="valiFormData.teamName" :clearable="false" :inputBorder="false" placeholder="请输入团队名称" />
				</uni-forms-item>
				<uni-forms-item label="手机号" required name="phone">
					<uni-easyinput type="number" v-model="valiFormData.phone" :clearable="false" :inputBorder="false" placeholder="请输入手机号" />
				</uni-forms-item>
				<uni-forms-item label="是否在职" required name="ifJob">
					<uni-data-checkbox v-model="valiFormData.ifJob" :localdata="ifJobDict" />
				</uni-forms-item>
			</view>
			<view class="example" style="padding-bottom: 20rpx;margin-top: 20rpx;">
				<view class="uni-forms-item" style="padding: 20rpx 0;display: flex;align-items: center;">
					<view class="uni-forms-item__label">
						<view><text class="is-required">*</text><text>头像面</text></view>
						<view style="color: #B2B2B2;font-size: 24rpx;margin-top: 20rpx;">上传您的身份证头像面</view>
					</view>
					<view class="grid-item-box">
						<img @click="picUP('frontUrl')" style="width: 260rpx;height: 168rpx;" :src="valiFormData.frontUrl?valiFormData.frontUrl:defaultImg" alt="" />
					</view>
				</view>
				<view class="uni-forms-item" style="padding: 20rpx 0;display: flex;align-items: center;margin-top: 20rpx;">
					<view class="uni-forms-item__label">
						<view><text class="is-required">*</text><text>国徽面</text></view>
						<view style="color: #B2B2B2;font-size: 24rpx;margin-top: 20rpx;">上传您的身份证国徽面</view>
					</view>
					<view class="grid-item-box">
						<img @click="picUP('contraryUrl')" style="width: 260rpx;height: 168rpx;" :src="valiFormData.contraryUrl?valiFormData.contraryUrl:defaultImg" alt="" />
					</view>
				</view>
				<view style="padding: 0 20rpx;border-radius: 20rpx;background-color: #F5F5F5;">
					<view class="uni-custom">
						<uni-forms-item label="姓名" name="name" >
							<uni-easyinput :styles="{background: '#F5F5F5'}" type="text" v-model="valiFormData.name" :clearable="false" :inputBorder="false" placeholder="请输入姓名" />
						</uni-forms-item>
					</view>
					<uni-forms-item label="身份证号" name="idNumber">
						<uni-easyinput :styles="{background: '#F5F5F5'}" type="idcard" v-model="valiFormData.idNumber" :clearable="false" :inputBorder="false" placeholder="请输入身份证号" />
					</uni-forms-item>
				</view>
			</view>	
			<view class="footer">
				<button type="primary" @click="submit('valiForm')">提交</button>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import { distributionApi, userApi } from '@/api/index.js';
	export default {
		name: 'orderList',
		data() {
			return {
				// 校验表单数据
				valiFormData: {
					teamName: '',
					phone: '',
					ifJob: null,
					contraryUrl:'',
					frontUrl: '',
					name: '',
					idNumber: '',
				},
				ifJobDict: [{
					text: '是',
					value: '1'
				},{
					text: '否',
					value: '0'
				}],
				defaultImg: '/static/images/img_just_data.png',
				// 校验规则
				rules: {
					teamName: {
						rules: [{
							required: true,
							errorMessage: '请输入团队名称'
						}]
					},
					phone: {
						rules: [{
							required: true,
							errorMessage: '请输入手机号'
						}]
					},
					ifJob: {
						rules: [{
							required: true,
							errorMessage: '请选择是否在职'
						}]
					},
					name: {
						rules: [{
							required: true,
							errorMessage: '请输入姓名'
						}]
					},
					idNumber: {
						rules: [{
							required: true,
							errorMessage: '请输入身份证号'
						}]
					},
				},
			}
		},
		onLoad: function(options) {
			if(options.params) {
				const params = JSON.parse(options.params)
				this.valiFormData = {...params}
				console.log(this.valiFormData)
			}
		},
		methods: {
			// 获取表单数据
			async submit(ref) {
				this.$refs[ref].validate().then(res => {
					console.log('success', res);
					res.frontUrl = this.valiFormData.frontUrl
					res.contraryUrl = this.valiFormData.contraryUrl
					res.id = this.valiFormData.id
					if(!res.frontUrl) {
						uni.showToast({
							title: '请上传头像面',
							icon: 'none'
						})
						return
					}
					if(!res.contraryUrl) {
						uni.showToast({
							title: '请上传国徽面',
							icon: 'none'
						})
						return
					}
					this.addAgent(res)
				}).catch(err => {
					console.log('err', err);
				})
			},
			// 新增表单
			addAgent(params) {
				distributionApi[this.valiFormData.id?'editTeam':'addTeam']({...params}).then((res) => {
					console.log(res)
					if (res.code !== 0) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					} else {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						})
						setTimeout(() => {
							// 进列表
							uni.navigateBack()
						}, 1000)
					}
				})
			},
			// 上传图片
			picUP: function(name) {
				var that = this
				uni.chooseImage({
					count: 1, //图片可选择数量
					sizeType: ['compressed'], //original 原图，compressed 压缩图，默认二者都有
					sourceType: ['album', 'camera'], //album 从相册选图，camera 使用相机，默认二者都有。
					extension: ['.png', '.jpg'], // 限制可选择的图片格式
					success: res => {
						that.uploadImg(res.tempFilePaths[0],name);
					},
					fail: res => {
					},
				});
			},
			async uploadImg(file,name) {
				try {
					const res = await userApi.uploadFile(file);
					if (res.data) {
						uni.showToast({
							title: '上传成功！'
						});
						if(name=='frontUrl'||name=='contraryUrl'){
							this.valiFormData[name] = res.data.url;
						}else{
							this.valiFormData[name].push(res.data.url)
						}
					}
				} catch (error) {
					console.error('获取失败:', error);
				}
			},
		}
	}
</script>


<style lang="scss">
	page{
		background-color: #F7F7F7;
	}
</style>
<style lang="scss" scoped>
	.page{
		padding: 30rpx;
		padding-bottom: 200rpx;
		.example{
			border-radius: 20rpx;
			background-color: white;
			padding: 0 24rpx;
		}
	}
	.audit_top{
		padding: 20rpx 24rpx;
		border-radius: 20rpx;
		background-image: linear-gradient(to right, #E8E8F6, #DFF2F6);
	}
	/deep/.uni-section{
		background-color: #F7F7F7;
	}
	/deep/.uni-section-header{
		padding: 24rpx 0 !important;
	}
	/deep/ .uni-section-header__decoration line{
		background-color: #7675FE;
	}
	/deep/ .uni-section .uni-section-header__content .distraction {
		font-weight: 600;
	}
	/deep/.uni-forms-item {
		justify-content: space-between;
		align-items: center;
	}
	/deep/.uni-forms-item__content {
		flex: none;
	}
	/deep/.uni-easyinput__content-input {
		text-align: right;
	}
	/deep/.uni-forms-item__label{
		width: auto !important;
		font-size: 28rpx !important;
		color: #333333;
		// font-weight: 600;
	}
	.uni-custom{
		/deep/ .uni-forms-item--border {
			border: none;
		}
	}
	.uni-forms-item__label{
		font-size: 28rpx;
		color: #333333;
	}
	.uni-forms-item .is-required {
	    color: #dd524d;
	    font-weight: bold;
	}
	.grid-item-box{
		position: relative;
		display: flex;
	}
	.del_round{
		width: 30rpx;
		height: 30rpx;
		position: absolute;
		right: 42rpx;
		top: 0;
		z-index: 5;
		background-color: #FFFFFF;
		border-radius: 50%;
	}
	.footer{
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 12rpx 30rpx;
		padding-bottom: 68rpx;
		z-index: 5;
		background-color: #F7F7F7;
		button{
			color: white;
			font-size: 32rpx;
			padding: 22rpx 0;
			background-color: #7675FE;
		}
	}
</style>