<template>
	<view class="page">
		<view class="timeline-container">
			<!-- 提现申请已提交 -->
			<view class="timeline-item active">
				<view class="timeline-dot active">
					<view class="dot-inner"></view>
				</view>
				<view class="timeline-content">
					<view class="timeline-title">提现申请已提交，等待处理</view>
					<view class="timeline-time">{{ detail.submitTime }}</view>
				</view>
			</view>
			
			<!-- 预计到账时间 -->
			<view class="timeline-item" :class="{ active: detail.status === 'success' }">
				<view class="timeline-dot" :class="{ active: detail.status === 'success' }">
					<view class="dot-inner" v-if="detail.status === 'success'"></view>
				</view>
				<view class="timeline-content">
					<view class="timeline-title">
						<text v-if="detail.status === 'pending'">预计到账时间</text>
						<text v-else-if="detail.status === 'success'">提现成功</text>
						<text v-else>提现失败</text>
					</view>
					<view class="timeline-time">
						<text v-if="detail.status === 'pending'">预计7个工作日打款</text>
						<text v-else-if="detail.status === 'success'">{{ detail.successTime }}</text>
						<text v-else>{{ detail.failTime }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				detail: {
					status: 'pending', // pending, success, failed
					submitTime: '02-13 11:30',
					successTime: '02-15 14:20',
					failTime: '02-14 09:15'
				}
			}
		},
		onLoad(options) {
			// 接收传递的参数
			if (options.status) {
				this.detail.status = options.status;
			}
			if (options.submitTime) {
				this.detail.submitTime = decodeURIComponent(options.submitTime);
			}

			// 设置页面标题
			uni.setNavigationBarTitle({
				title: '提现明细'
			});
		}
	}
</script>

<style scoped>
	.page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 40rpx 24rpx;
	}

	.timeline-container {
		position: relative;
	}

	.timeline-item {
		display: flex;
		align-items: flex-start;
		position: relative;
		padding-bottom: 60rpx;
	}

	.timeline-item:last-child {
		padding-bottom: 0;
	}

	.timeline-item::after {
		content: '';
		position: absolute;
		left: 24rpx;
		top: 48rpx;
		width: 4rpx;
		height: calc(100% - 48rpx);
		background-color: #e5e5e5;
	}

	.timeline-item:last-child::after {
		display: none;
	}

	.timeline-item.active::after {
		background-color: #4D40E5;
	}

	.timeline-dot {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		background-color: #cccccc;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		flex-shrink: 0;
		position: relative;
		z-index: 1;
	}

	.timeline-dot.active {
		background-color: #4D40E5;
	}

	.dot-inner {
		width: 24rpx;
		height: 24rpx;
		border-radius: 50%;
		background-color: #ffffff;
	}

	.timeline-content {
		flex: 1;
		padding-top: 8rpx;
	}

	.timeline-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
		margin-bottom: 8rpx;
	}

	.timeline-time {
		font-size: 26rpx;
		color: #999999;
	}
</style>
