<template>
	<view class="reset-password-container">
		
		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 原密码输入框 (仅在非短信验证时显示) -->
			<view v-if="!isFromSms" class="input-section">
				<view class="section-title">原密码</view>
				<view class="input-container">
					<view class="input-wrapper">
						<input 
							class="password-input" 
							:type="showOldPassword ? 'text' : 'password'"
							placeholder="请输入原密码" 
							v-model="oldPassword"
						/>
						<view class="eye-icon" @click="toggleOldPasswordVisibility">
							<text class="eye-text">{{ showOldPassword ? '👁' : '🙈' }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 新密码输入框 -->
			<view class="input-section">
				<view class="section-title">新密码</view>
				<view class="input-container">
					<view class="input-wrapper">
						<input
							class="password-input"
							:type="showNewPassword ? 'text' : 'password'"
							placeholder="请输入新密码"
							v-model="newPassword"
						/>
						<view class="eye-icon" @click="toggleNewPasswordVisibility">
							<text class="eye-text">{{ showNewPassword ? '👁' : '🙈' }}</text>
						</view>
					</view>
					<view class="confirm-input-wrapper">
						<input
							class="password-input confirm-input"
							:type="showConfirmPassword ? 'text' : 'password'"
							placeholder="请再一次输入新密码"
							v-model="confirmPassword"
						/>
						<view class="eye-icon" @click="toggleConfirmPasswordVisibility">
							<text class="eye-text">{{ showConfirmPassword ? '👁' : '🙈' }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 密码要求提示 -->
			<view class="password-hint">
				<text>密码需设置为8-20位，且英文字母、数字和标点符号至少包含两种，不允许有空格、中文。</text>
			</view>
			
			<!-- 完成按钮 -->
			<view class="complete-btn-container">
				<button class="complete-btn" @click="handleComplete">完成</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isFromSms: false, // 是否来自短信验证码找回
			oldPassword: '',
			newPassword: '',
			confirmPassword: '',
			showOldPassword: false,
			showNewPassword: false,
			showConfirmPassword: false,
			phoneNumber: '' // 从上一页传递的手机号
		}
	},
	
	onLoad(options) {
		// 如果有手机号参数，说明是从短信验证码找回密码
		if (options.phone) {
			this.isFromSms = true;
			this.phoneNumber = options.phone;
		}
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 切换原密码显示/隐藏
		toggleOldPasswordVisibility() {
			this.showOldPassword = !this.showOldPassword;
		},
		
		// 切换新密码显示/隐藏
		toggleNewPasswordVisibility() {
			this.showNewPassword = !this.showNewPassword;
		},
		
		// 切换确认密码显示/隐藏
		toggleConfirmPasswordVisibility() {
			this.showConfirmPassword = !this.showConfirmPassword;
		},
		
		// 验证密码格式
		validatePassword(password) {
			// 8-20位，且英文字母、数字和标点符号至少包含两种，不允许有空格、中文
			if (password.length < 8 || password.length > 20) {
				return false;
			}
			
			// 检查是否包含空格或中文
			if (/[\s\u4e00-\u9fa5]/.test(password)) {
				return false;
			}
			
			// 检查是否至少包含两种类型的字符
			let hasLetter = /[a-zA-Z]/.test(password);
			let hasNumber = /[0-9]/.test(password);
			let hasSymbol = /[^a-zA-Z0-9]/.test(password);
			
			let typeCount = 0;
			if (hasLetter) typeCount++;
			if (hasNumber) typeCount++;
			if (hasSymbol) typeCount++;
			
			return typeCount >= 2;
		},
		
		// 完成处理
		handleComplete() {
			// 如果不是短信验证码找回，需要验证原密码
			if (!this.isFromSms && !this.oldPassword) {
				uni.showToast({
					title: '请输入原密码',
					icon: 'none'
				});
				return;
			}
			
			if (!this.newPassword) {
				uni.showToast({
					title: '请输入新密码',
					icon: 'none'
				});
				return;
			}
			
			if (!this.confirmPassword) {
				uni.showToast({
					title: '请确认新密码',
					icon: 'none'
				});
				return;
			}
			
			// 验证新密码格式
			if (!this.validatePassword(this.newPassword)) {
				uni.showToast({
					title: '密码格式不符合要求',
					icon: 'none'
				});
				return;
			}
			
			// 验证两次密码是否一致
			if (this.newPassword !== this.confirmPassword) {
				uni.showToast({
					title: '两次输入的密码不一致',
					icon: 'none'
				});
				return;
			}
			
			// 这里添加修改密码的API调用
			console.log('修改密码:', {
				isFromSms: this.isFromSms,
				phoneNumber: this.phoneNumber,
				oldPassword: this.oldPassword,
				newPassword: this.newPassword
			});
			
			uni.showToast({
				title: '密码修改成功',
				icon: 'success'
			});
			
			// 延迟跳转到登录页
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}, 1500);
		}
	}
}
</script>

<style scoped>
.reset-password-container {
	width: 100%;
	min-height: 100vh;
	background-color: #F6F8FF;
}

/* 导航栏 */
.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 30rpx;
	background-color: #F6F8FF;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	font-size: 48rpx;
	color: #333333;
	font-weight: bold;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333333;
}

.nav-right {
	width: 60rpx;
}

/* 主要内容 */
.main-content {
	padding: 60rpx 30rpx;
}

/* 输入区域 */
.input-section {
	margin-bottom: 60rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #000000;
	margin-bottom: 30rpx;
}

.input-container {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	overflow: hidden;
}

.input-wrapper {
	position: relative;
	height: 120rpx;
	display: flex;
	align-items: center;
}

.confirm-input-wrapper {
	position: relative;
	height: 120rpx;
	display: flex;
	align-items: center;
	border-top: 1rpx solid #F0F0F0;
}

.password-input {
	flex: 1;
	height: 100%;
	padding: 0 80rpx 0 30rpx;
	font-size: 32rpx;
	color: #333333;
	border: none;
	background-color: transparent;
}

.password-input::placeholder {
	color: #CCCCCC;
}

/* 眼睛图标 */
.eye-icon {
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.eye-text {
	font-size: 32rpx;
	color: #CCCCCC;
}

/* 密码提示 */
.password-hint {
	margin-bottom: 80rpx;
	padding: 0 10rpx;
}

.password-hint text {
	font-size: 24rpx;
	color: #999999;
	line-height: 1.5;
}

/* 完成按钮 */
.complete-btn-container {
	margin-top: 120rpx;
}

.complete-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
	border-radius: 20rpx;
	border: none;
	color: #FFFFFF;
	font-size: 36rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

.complete-btn:active {
	opacity: 0.8;
}
</style>
