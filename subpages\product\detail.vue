<template>
	<view class="product-detail-page">
		<!-- 自定义导航栏 -->
		<custom-navbar 
			title-color="#000000"
			icon-color="#000000"
			background-color="transparent">
		</custom-navbar>
		
		<!-- 商品轮播图 -->
		<view class="swiper-container">
			<swiper 
				class="product-swiper" 
				:indicator-dots="false" 
				:autoplay="true" 
				:interval="3000" 
				:duration="500"
				@change="onSwiperChange">
				<swiper-item v-for="(image, index) in productImages" :key="index">
					<image :src="image" mode="aspectFill" class="swiper-image"></image>
				</swiper-item>
			</swiper>
			
			<!-- 轮播图指示器 -->
			<view class="swiper-indicator">
				<text class="indicator-text">{{ currentImageIndex + 1 }}/{{ productImages.length }}</text>
			</view>
		</view>
		
		<!-- 商品信息区域 -->
		<view class="product-info-section">
			<!-- 价格和商品名称 -->
			<view class="price-name-container">
				<view class="price-wrapper">
					<text class="price-symbol">¥</text>
					<text class="price-amount">{{ Math.floor(productInfo.price) }}</text>
					<text class="price-decimal" v-if="productInfo.price % 1 !== 0">.{{ String(productInfo.price).split('.')[1] || '00' }}</text>
				</view>
				<view class="product-name">
					<text class="name-text">{{ productInfo.name }}</text>
				</view>
				<!-- 商品描述 -->
				<view class="product-describe" v-if="productInfo.describe">
					<text class="describe-text">{{ productInfo.describe }}</text>
				</view>
			</view>
			
			<!-- 详情区域 -->
			<view class="detail-section">
				<view class="detail-header">
					<text class="detail-title">详情</text>
				</view>
				<view class="detail-content">
					<!-- 商品详情HTML内容 -->
					<rich-text
						v-if="productInfo.detail"
						:nodes="productInfo.detail"
						class="detail-rich-text">
					</rich-text>
					<view v-else class="detail-placeholder">
						<text class="placeholder-text">暂无详情信息</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部固定按钮 -->
		<view class="bottom-fixed-bar">
			<view class="checkout-btn" @click="handleCheckout">
				<text class="checkout-text">立即结算</text>
			</view>
		</view>
	</view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar/custom-navbar.vue'
import { goodsApi } from '@/api/index.js'

export default {
	name: 'ProductDetail',
	components: {
		CustomNavbar
	},
	data() {
		return {
			goodsId: '', // 商品ID
			currentImageIndex: 0,
			productImages: [
				'/static/images/drawable-hdpi_bg_home.png',
				'/static/images/img_bg_my.png',
				'/static/images/tixianbg.jpg'
			],
			productInfo: {
				id: '',
				name: '名称名称名称名称名称名称名称名称台礼包名称名称名称名称名称名称名称台礼包',
				price: 635.84,
				images: [],
				detail: ''
			}
		}
	},
	onLoad(options) {
		// 获取商品ID
		if (options.goodsId) {
			this.goodsId = options.goodsId;
			this.productInfo.id = options.goodsId;
			this.loadProductDetail();
		} else if (options.id) {
			// 兼容旧的参数名
			this.goodsId = options.id;
			this.productInfo.id = options.id;
			this.loadProductDetail();
		}
	},
	methods: {
		// 轮播图切换事件
		onSwiperChange(e) {
			this.currentImageIndex = e.detail.current;
		},
		
		// 加载商品详情
		async loadProductDetail() {
			try {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});

				const result = await goodsApi.getGoodsDetail(this.goodsId);

				uni.hideLoading();

				if (result.code === 0 || result.code === 200) {
					const goodsData = result.data.result;
					console.log('商品详情数据:', goodsData);

					// 映射商品信息
					this.productInfo = {
						id: goodsData.id,
						name: goodsData.goodsName,
						price: goodsData.price,
						specification: goodsData.specification,
						describe: goodsData.goodsDescribe,
						detail: goodsData.goodsDetailInfo,
						status: goodsData.status,
						freight: goodsData.freight
					};

					// 设置商品图片（轮播图）
					if (goodsData.goodsImg) {
						this.productImages = [goodsData.goodsImg];
					}
				}
			} catch (error) {
				uni.hideLoading();
				console.error('加载商品详情失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			}
		},
		
		// 立即结算
		handleCheckout() {
			// 验证商品信息是否加载完成
			if (!this.productInfo.id) {
				uni.showToast({
					title: '商品信息加载中，请稍后',
					icon: 'none'
				});
				return;
			}

			// 构造商品数据传递给订单确认页面
			const productData = encodeURIComponent(JSON.stringify({
				goodsId: this.goodsId,
				id: this.productInfo.id,
				name: this.productInfo.name,
				price: this.productInfo.price,
				image: this.productImages[0] || '',
				specification: this.productInfo.specification,
				freight: this.productInfo.freight || 0
			}));

			uni.navigateTo({
				url: `/subpages/order/confirm?productData=${productData}`
			});
		}
	}
}
</script>

<style scoped>
.product-detail-page {
	background-color: #F5F5F5;
	min-height: 100vh;
	padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 轮播图容器 */
.swiper-container {
	position: relative;
	width: 100%;
	height: 750rpx;
	background-color: #ffffff;
}

.product-swiper {
	width: 100%;
	height: 100%;
}

.swiper-image {
	width: 100%;
	height: 100%;
}

/* 轮播图指示器 */
.swiper-indicator {
	position: absolute;
	bottom: 30rpx;
	right: 30rpx;
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 30rpx;
	padding: 8rpx 20rpx;
}

.indicator-text {
	color: #ffffff;
	font-size: 24rpx;
}

/* 商品信息区域 */
.product-info-section {
	margin-top: 20rpx;
}

/* 价格和商品名称容器 */
.price-name-container {
	background-color: #F5F5F5;
	padding: 30rpx;
}

.price-wrapper {
	display: flex;
	align-items: baseline;
	margin-bottom: 20rpx;
}

.price-symbol {
	color: #FF2222;
	font-size: 32rpx;
	font-weight: bold;
}

.price-amount {
	color: #FF2222;
	font-size: 48rpx;
	font-weight: bold;
}

.price-decimal {
	color: #FF2222;
	font-size: 32rpx;
	font-weight: bold;
}

.product-name {
	margin-top: 10rpx;
}

.name-text {
	color: #333333;
	font-size: 32rpx;
	line-height: 1.5;
}

/* 商品描述 */
.product-describe {
	margin-top: 20rpx;
	padding: 16rpx 20rpx;
	background-color: #fff5f5;
	border-radius: 8rpx;
	border-left: 6rpx solid #ff6b6b;
}

.describe-text {
	font-size: 28rpx;
	color: #ff6b6b;
	font-weight: 500;
}

/* 详情区域 */
.detail-section {
	background-color: #ffffff;
	border-radius: 20rpx;
	margin: 20rpx 30rpx;
	padding: 30rpx;
}

.detail-header {
	margin-bottom: 30rpx;
}

.detail-title {
	color: #333333;
	font-size: 32rpx;
	font-weight: bold;
}

.detail-content {
	min-height: 400rpx;
}

.detail-placeholder {
	width: 100%;
	height: 400rpx;
	background-color: #f8f8f8;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.detail-rich-text {
	width: 100%;
}

.placeholder-text {
	color: #999999;
	font-size: 28rpx;
}

/* 底部固定按钮 */
.bottom-fixed-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.checkout-btn {
	background-color: #4D40E5;
	border-radius: 20rpx;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.checkout-text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: bold;
}
</style>
