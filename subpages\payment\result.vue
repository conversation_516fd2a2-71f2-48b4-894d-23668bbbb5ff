<template>
	<view class="payment-result-page">
		<!-- 结果图标和状态 -->
		<view class="result-section">
			<view class="result-icon-wrapper">
				<image :src="resultIcon" class="result-icon" mode="aspectFit"></image>
			</view>
			
			<view class="result-text-wrapper">
				<text class="result-title">{{ resultTitle }}</text>
			</view>
			
			<!-- <view class="amount-wrapper" v-if="paymentStatus === 'success'">
				<text class="amount-label">支付金额</text>
				<view class="amount-value-wrapper">
					<text class="currency-symbol">¥</text>
					<text class="amount-value">{{ paymentAmount }}</text>
				</view>
			</view> -->
		</view>
		
		<!-- 底部按钮区域 -->
		<view class="bottom-actions">
			<view class="action-btn secondary-btn" @click="goToHome">
				<text class="btn-text secondary-text">返回首页</text>
			</view>

			<view class="action-btn primary-btn" @click="goToOrders">
				<text class="btn-text primary-text">查看订单</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PaymentResult',
	data() {
		return {
			paymentStatus: 'success', // success 或 fail
			paymentAmount: '127.99'
		}
	},
	computed: {
		resultIcon() {
			return this.paymentStatus === 'success' 
				? '/static/images/img_succeed_default.png'
				: '/static/images/img_lose_default.png';
		},
		
		resultTitle() {
			return this.paymentStatus === 'success' ? '支付成功' : '支付失败';
		}
	},
	onLoad(options) {
		// 获取支付状态和金额
		if (options.status) {
			this.paymentStatus = options.status;
		}
		if (options.amount) {
			this.paymentAmount = options.amount;
		}
	},
	methods: {
		// 返回首页
		goToHome() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		},

		// 查看订单
		goToOrders() {
			uni.navigateTo({
				url: '/subpages/order/list'
			});
		}
	}
}
</script>

<style scoped>
.payment-result-page {
	background-color: #F5F5F5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	padding-bottom: 140rpx; /* 为底部按钮留出空间 */
}

/* 结果展示区域 */
.result-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: flex-start;
	padding-top: 50rpx;
}

.result-icon-wrapper {
	/* margin-bottom: 40rpx; */
}

.result-icon {
	width: 360rpx;
	height: 360rpx;
}

.result-text-wrapper {
	margin-bottom: 60rpx;
}

.result-title {
	color: #333333;
	font-size: 36rpx;
}

/* 支付金额显示（仅成功时显示） */
.amount-wrapper {
	text-align: center;
}

.amount-label {
	color: #666666;
	font-size: 28rpx;
	display: block;
	margin-bottom: 20rpx;
}

.amount-value-wrapper {
	display: flex;
	align-items: baseline;
	justify-content: center;
}

.currency-symbol {
	color: #333333;
	font-size: 32rpx;
	font-weight: bold;
	margin-right: 8rpx;
}

.amount-value {
	color: #333333;
	font-size: 48rpx;
	font-weight: bold;
}

/* 底部按钮区域 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 30rpx;
	padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
	display: flex;
	gap: 20rpx;
	top: 550rpx;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.secondary-btn {
	background-color: #F5F5F5;
	border: 1rpx solid #E5E5E5;
}

.secondary-btn:active {
	background-color: #EEEEEE;
}

.primary-btn {
	background-color: #4D40E5;
}

.primary-btn:active {
	background-color: #3d32d1;
}

.btn-text {
	font-size: 32rpx;
	font-weight: bold;
}

.secondary-text {
	color: #666666;
}

.primary-text {
	color: #ffffff;
}
</style>
