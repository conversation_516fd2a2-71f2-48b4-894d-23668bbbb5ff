<template>
	<view class="container">
		<view class="bg">
			<img style="width: 100%;" mode="widthFix" src="/static/images/img_bg_my.png" alt="" />
		</view>
		<!-- 用户信息模块 -->
		<view class="user-section">
			<view class="user-info">
				<image class="avatar" :src="userAvatar" mode="aspectFill"></image>
				<view class="user-details">
					<view class="user-content">
						<view class="user-text">
							<view class="username">
								{{ userName }}
								<text class="team-tag" v-if="userTypeText">{{ userTypeText }}</text>
							</view>
							<view class="phone">账号：{{ userPhone }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="user-amount">
			<view class="" style="display: flex;padding: 30rpx;justify-content: space-between;align-items: center;">
				<view>
					<view style="color: #999999;font-size: 24rpx;">累计收益(元)</view>
					<view style="font-size: 52rpx;margin-top: 10rpx;font-weight: 600;">{{todayStats.allFenrun}}</view>
				</view>
				<view style="display: flex;">
					<img style="width: 118rpx;height: 118rpx;" src="/static/images/img_ceding_index.png" alt="" />
				</view>
			</view>
			<view style="display: flex;padding: 0 30rpx 30rpx 30rpx;justify-content: space-between;align-items: center;">
				<view>
					<text style="color: #999999;font-size: 24rpx;">今日收益(元) </text>
					<text style="font-size: 32rpx;font-weight: 600;margin-left: 10rpx;">+{{todayStats.dayFenrun}}</text>
				</view>
				<view>
					<text style="color: #999999;font-size: 24rpx;"></text>昨日收益(元)
					<text style="font-size: 32rpx;font-weight: 600;margin-left: 10rpx;">+{{todayStats.zrFenrun}}</text>
				</view>
			</view>
			<view style="border-top: 1px solid #F6F8FF;padding: 20rpx 30rpx;font-size: 24rpx;display: flex;align-items: center;">
				<swiper class="swiper" :disable-touch="true" circular vertical :autoplay="true" :interval="10000"
					:duration="duration">
					<swiper-item v-for="(item, index) in notices" :key="index" @click="onList('/subpages/notice/noticesList')">
						<view class="swiper-item uni-bg-red">
							<view style="display: flex;">
								<img style="width: 40rpx;height: 40rpx;" src="/static/images/img_notice.png" alt="" />
							</view>
							<view class="text-ellipsis_1" style="margin-left: 16rpx;color: #666666;font-size: 24rpx;">{{item.content}}</view>
						</view>
					</swiper-item>
				</swiper>
			</view>
		</view>
		<view class="stats-card">
			<view class="stats-grid">
				<view class="stats-item" @click="onList('/subpages/agent/agent')">
					<view class="stats-number">{{ todayStats.agentDay }}</view>
					<view class="stats-label"><view>今日新增代理商</view><view style="margin-left: 4rpx;" class="right-icon"></view></view>
				</view>
				<view class="stats-item" @click="onList('/subpages/team/team')">
					<view class="stats-number">{{ todayStats.teamDay }}</view>
					<view class="stats-label"><view>今日新增团队</view><view style="margin-left: 4rpx;" class="right-icon"></view></view>
				</view>
				<view class="stats-item" @click="onList('/subpages/merchant/merchant')">
					<view class="stats-number">{{ todayStats.merDay }}</view>
					<view class="stats-label"><view>今日新增商户</view><view style="margin-left: 4rpx;" class="right-icon"></view></view>
				</view>
			</view>
		</view>
		<!-- 工作台 -->
		<view class="stats-card work_bench">
			<view class="stats-title" style="display: flex;font-size: 36rpx;align-items: center;padding: 10rpx 0 0 20rpx">
				<view>工作台</view>
				<view style="font-weight: normal;color: #666666;font-size: 24rpx;margin-left: 10rpx;margin-top: 4rpx;">新增角色赚取佣金</view>
			</view>
			<view class="stats-grid work_bench_grid">
				<view class="stats-item work_bench_1" @click="onList('/subpages/agent/addAgent')">
					<img class="work_bench_img" src="/static/images/icon_adddls.png" alt="" />
					<view class="stats-number work_bench_number">新增代理商</view>
					<view class="stats-label work_bench_label">赚取佣金</view>
				</view>
				<view class="stats-item work_bench_2" @click="onList('/subpages/team/addTeam')">
					<img class="work_bench_img" src="/static/images/icon_addteam.png" alt="" />
					<view class="stats-number work_bench_number">新增团队</view>
					<view class="stats-label work_bench_label">赚取佣金</view>
				</view>
				<view class="stats-item work_bench_3" @click="onList('/subpages/merchant/addMerchant')">
					<img class="work_bench_img" src="/static/images/icon_addshop.png" alt="" />
					<view class="stats-number work_bench_number">新增商户</view>
					<view class="stats-label work_bench_label">赚取佣金</view>
				</view>
			</view>
		</view>
		<view class="shangxueyuan" @click="onDetails('/subpages/news/detail?id=', pic.id)">
			<view>
				<view class="text-ellipsis_2" style="font-size: 28rpx;">{{pic.intro}}</view>
				<view style="color: #999999;font-size: 24rpx;margin-top: 40rpx;">{{pic.createDate}}</view>
			</view>
			<view style="display: flex;"><img style="width: 220rpx;height: 140rpx;border-radius: 14rpx;" :src="pic.picUrl" alt="图片加载中" /></view>
		</view>

		<!-- 热门产品 -->
		<view class="product-section">
			<view class="section-header">
				<img style="height: 48rpx;width: 338rpx;"  src="/static/images/icon_tuijian.png" alt="" />
			</view>
			<view class="product-list">
				<view class="product-item" v-for="(item, index) in productList" :key="index" @click="onDetails('/subpages/product/detail?id=',item.id)">
					<image class="product-image" :src="item.goodsImg" mode="widthFix"></image>
					<view class="product-info">
						<view class="product-title text-ellipsis_2">{{ item.goodsName }}</view>
						<view class="product-price">¥{{ item.price }}</view>
						<!-- <view class="product-commission">佣金：¥{{ item.commission }}</view> -->
					</view>
				</view>
			</view>
			<uni-load-more iconType="circle" :status="status" />
		</view>
	</view>
</template>

<script>
	import { userApi, productApi, systemApi } from '@/api/index.js';
	export default {
		data() {
			return {
				userInfo: null, // 用户信息
				userName: '用户名', // 用户名
				userPhone: '', // 手机号
				userAvatar: '/static/images/default-avatar.png', // 头像
				userType: '', // 用户类型
				userTypeText: '', // 用户类型文本
				todayStats: {
					allFenrun: 0,
					dayFenrun: 0,
					zrFenrun: 0,
					agentDay: 0,
					teamDay: 0,
					merDay: 0
				},
				pic: {},
				productList: [],
				notices: [],
				status: 'noMore', // more,loading,noMore
				limit: 10,
				page: 1
				
			}
		},
		async onLoad() {
			await this.getUserInfo();
			await this.getProductList();
			await this.getNotices();
		},
		async onShow() {
			// 页面显示时也调用，确保数据是最新的
			// this.productList = []
			await this.init();
		},
		methods: {
			async init() {
				const res = await systemApi.getIndex()
				this.todayStats = res.data
				this.pic = res.data.pic
			},
			// 获取用户信息
			async getUserInfo() {
				try {
					const res = await userApi.getUserInfo();
					console.log('用户信息:', res);
					this.userInfo = res.data;
			
					// 处理用户数据
					if (res.data && res.data.user) {
						const user = res.data.user;
						this.userName = user.userName || '用户名';
						this.userPhone = user.phone || '';
						this.userAvatar = user.imgUrl || '/static/images/default-avatar.png';
						this.userType = user.linkType || '';
			
						// 根据 linkType 设置用户类型文本
						switch (user.linkType) {
							case '0':
								this.userTypeText = '代理商';
								break;
							case '1':
								this.userTypeText = '团队';
								break;
							case '2':
								this.userTypeText = '员工';
								break;
							default:
								this.userTypeText = '';
								break;
						}
					}
				} catch (error) {
					console.error('获取用户信息失败:', error);
				}
			},
			// 公告
			async getNotices() {
				try {
					const res = await systemApi.getNotices({
						  "limit": 10,
						  "page": 1,
						  "type": "0"
					});
					console.log('我的账户信息:', res);
			
					// 处理账户数据
					if (res.data) {
						this.notices = res.data.total>0?res.data.records:['暂无公告内容']
					}
				} catch (error) {
					console.error('获取账户信息失败:', error);
				}
			},
			// 获取商品列表
			async getProductList() {
				try {
					const res = await productApi.getProductList();
					if (res.data) {
						this.productList = res.data
					}
				} catch (error) {
					console.error('获取账户信息失败:', error);
				}
			},
			onDetails(url,id) {
				uni.navigateTo({
					url: `${url}${id}`
				})
			},
			onList(url) {
				uni.navigateTo({
					url: url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #F6F8FF;
		padding: 0 30rpx;
		.bg{
			position: absolute;
			left: 0;
			top: 0;
			right: 0;
			display: flex;
			z-index: 1;
		}
		/* 用户信息模块 */
		.user-section {
			position: relative;
			z-index: 5;
			margin-bottom: 40rpx;
			margin-top: 150rpx;
		}

		.user-info {
			display: flex;
			align-items: center;
		}

		.avatar {
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
			margin-right: 30rpx;
			border: 6rpx solid rgba(255,255,255,1);
		}

		.user-details {
			flex: 1;
			color: white;
		}

		.user-content {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.user-text {
			flex: 1;
		}

		.username {
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 15rpx;
			display: flex;
			align-items: center;
		}

		.setup-icon {
			width: 55rpx;
			height: 55rpx;
		}

		.team-tag {
			background-color: rgba(0,0,0,0.2);
			padding: 6rpx 16rpx;
			border-radius: 20rpx;
			font-size: 22rpx;
			margin-left: 15rpx;
		}

		.phone {
			font-size: 28rpx;
			opacity: 0.6;
		}
		.user-amount{
			background-color: #ffffff;
			position: relative;
			z-index: 5;
			border-radius: 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
		}
	}
	.uni-margin-wrap {
		width: 690rpx;
		width: 100%;
	}
	.swiper {
		width: 100%;
		height: 40rpx;
	}
	.swiper-item {
		display: flex;
		align-items: center;
		// height: 300rpx;
		// line-height: 300rpx;
		// text-align: center;
	}

	.stats-card {
		margin-top: 20rpx;
		background: white;
		border-radius: 20rpx;
		padding: 20rpx 10rpx 10rpx;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	}
	.work_bench{
		border-radius: 30rpx;
		background-image: linear-gradient(to right,#DCDEFF, #CBF0FF);
	}

	.stats-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;
	}
	.work_bench_grid{
		position: relative;
		background-color: white;
		border-radius: 30rpx;
		padding: 40rpx 20rpx 20rpx;
	}

	.stats-item {
		position: relative;
		text-align: center;
		padding: 20rpx 0;
		border-radius: 10rpx;
		// background: #f8f9fa;
	}
	.work_bench_img{
		position: absolute;
		width: 70rpx;
		height: 70rpx;
		left: 50%;
		margin-left: -35rpx;
		top: -35rpx;
	}
	.stats-number {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}
	.work_bench_1{
		padding-top: 35rpx;
		border-radius: 20rpx;
		background-image: linear-gradient(to top, #FFEADD, #FEE2DC);
	}
	.work_bench_2{
		padding-top: 35rpx;
		border-radius: 20rpx;
		background-image: linear-gradient(to top, #E2E1FF, #EBE7FF);
	}
	.work_bench_3{
		padding-top: 35rpx;
		border-radius: 20rpx;
		background-image: linear-gradient(to top, #FFDEE9, #FFE7EB);
	}
	.work_bench_number{
		font-size: 26rpx;
	}
	.work_bench_label{
		font-size: 20rpx;
		color: #999999;
	}

	.stats-label {
		font-size: 24rpx;
		color: #666666;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.shangxueyuan{
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 24rpx;
		background-color: #fff;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	}

	.product-section {
		margin-top: 20rpx;
	}

	.section-header {
		display: flex;
		padding: 20rpx 0 30rpx 0;
		background-color: #F6F8FF;
	}

	.section-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}

	.section-more {
		font-size: 24rpx;
		color: #999;
	}

	.product-list {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
	}

	.product-item {
		background: white;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	}

	.product-image {
		width: 100%;
		height: 200rpx;
		background-color: #f0f0f0;
	}

	.product-info {
		padding: 20rpx;
	}

	.product-title {
		font-size: 26rpx;
		color: #333;
		margin-bottom: 10rpx;
	}

	.product-price {
		padding-top: 20rpx;
		font-size: 28rpx;
		// color: #ff6b6b;
		font-weight: bold;
		margin-bottom: 5rpx;
	}

	.product-commission {
		font-size: 22rpx;
		color: #52c41a;
	}
</style>
