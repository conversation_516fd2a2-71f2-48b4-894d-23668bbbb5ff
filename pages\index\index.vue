<template>
	<view class="container">
		<!-- 轮播图 -->
		<swiper class="banner" indicator-dots="true" autoplay="true" interval="3000" duration="500">
			<swiper-item v-for="(item, index) in bannerList" :key="index">
				<image class="banner-image" :src="item.image" mode="aspectFill"></image>
			</swiper-item>
		</swiper>

		<!-- 快捷功能 -->
		<view class="quick-menu">
			<view class="quick-item" v-for="(item, index) in quickMenuList" :key="index" @click="handleQuickMenu(item)">
				<view class="quick-icon" :style="{backgroundColor: item.bgColor}">{{ item.icon }}</view>
				<view class="quick-title">{{ item.title }}</view>
			</view>
		</view>

		<!-- 数据统计 -->
		<view class="stats-card">
			<view class="stats-title">今日数据</view>
			<view class="stats-grid">
				<view class="stats-item">
					<view class="stats-number">{{ todayStats.orders }}</view>
					<view class="stats-label">今日订单</view>
				</view>
				<view class="stats-item">
					<view class="stats-number">{{ todayStats.earnings }}</view>
					<view class="stats-label">今日收益</view>
				</view>
				<view class="stats-item">
					<view class="stats-number">{{ todayStats.customers }}</view>
					<view class="stats-label">新增客户</view>
				</view>
			</view>
		</view>

		<!-- 热门产品 -->
		<view class="product-section">
			<view class="section-header">
				<view class="section-title">热门产品</view>
				<view class="section-more">更多 ></view>
			</view>
			<view class="product-list">
				<view class="product-item" v-for="(item, index) in productList" :key="index">
					<image class="product-image" :src="item.image" mode="aspectFill"></image>
					<view class="product-info">
						<view class="product-title">{{ item.title }}</view>
						<view class="product-price">¥{{ item.price }}</view>
						<view class="product-commission">佣金：¥{{ item.commission }}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bannerList: [
					{ image: '/static/images/banner1.jpg' },
					{ image: '/static/images/banner2.jpg' },
					{ image: '/static/images/banner3.jpg' }
				],
				quickMenuList: [
					{ title: '推广商品', icon: '🛍️', bgColor: '#FF6B6B' },
					{ title: '邀请好友', icon: '👥', bgColor: '#4ECDC4' },
					{ title: '订单管理', icon: '📋', bgColor: '#45B7D1' },
					{ title: '客户管理', icon: '👤', bgColor: '#96CEB4' }
				],
				todayStats: {
					orders: 12,
					earnings: '268.50',
					customers: 5
				},
				productList: [
					{
						title: '精选商品A',
						price: '199.00',
						commission: '19.90',
						image: '/static/images/product1.jpg'
					},
					{
						title: '热销商品B',
						price: '299.00',
						commission: '29.90',
						image: '/static/images/product2.jpg'
					}
				]
			}
		},
		onLoad() {
			this.loadData();
		},
		methods: {
			loadData() {
				// 加载数据的方法
				console.log('加载首页数据');
			},
			handleQuickMenu(item) {
				console.log('点击快捷菜单：', item.title);
				// 处理快捷菜单点击
			}
		}
	}
</script>

<style scoped>
	.container {
		min-height: 100vh;
		background-color: #f8f8f8;
	}

	.banner {
		height: 300rpx;
	}

	.banner-image {
		width: 100%;
		height: 100%;
		background-color: #e0e0e0;
	}

	.quick-menu {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20rpx;
		padding: 30rpx;
		background: white;
	}

	.quick-item {
		text-align: center;
		padding: 20rpx 0;
	}

	.quick-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 15rpx;
		font-size: 32rpx;
	}

	.quick-title {
		font-size: 24rpx;
		color: #333;
	}

	.stats-card {
		background: white;
		margin: 20rpx 30rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	}

	.stats-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;
	}

	.stats-item {
		text-align: center;
		padding: 20rpx 0;
		border-radius: 10rpx;
		background: #f8f9fa;
	}

	.stats-number {
		font-size: 32rpx;
		font-weight: bold;
		color: #5856D6;
		margin-bottom: 10rpx;
	}

	.stats-label {
		font-size: 22rpx;
		color: #666;
	}

	.product-section {
		margin: 20rpx 30rpx;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.section-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}

	.section-more {
		font-size: 24rpx;
		color: #999;
	}

	.product-list {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
	}

	.product-item {
		background: white;
		border-radius: 15rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	}

	.product-image {
		width: 100%;
		height: 200rpx;
		background-color: #f0f0f0;
	}

	.product-info {
		padding: 20rpx;
	}

	.product-title {
		font-size: 26rpx;
		color: #333;
		margin-bottom: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.product-price {
		font-size: 28rpx;
		color: #ff6b6b;
		font-weight: bold;
		margin-bottom: 5rpx;
	}

	.product-commission {
		font-size: 22rpx;
		color: #52c41a;
	}
</style>
