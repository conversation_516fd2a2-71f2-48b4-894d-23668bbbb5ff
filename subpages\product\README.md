# 商品详情页面

## 页面概述

按照设计图1:1完整还原的商品详情页面，包含以下功能模块：

## 页面结构

### 1. 自定义导航栏
- 使用 `custom-navbar` 组件
- 透明背景，黑色图标和文字
- 支持返回功能

### 2. 商品轮播图
- 使用 `swiper` 组件实现图片轮播
- 自动播放，间隔3秒
- 右下角显示当前图片索引（如：1/3）
- 支持手动滑动切换

### 3. 商品信息区域
- **背景色**: #F5F5F5
- **价格显示**: 
  - 颜色: #FF2222
  - 格式: ¥635.84
  - 大字体显示
- **商品名称**: 
  - 支持长文本显示
  - 自动换行

### 4. 详情区域
- **背景**: 白色圆角容器
- **标题**: "详情"
- **内容区域**: 预留空间，可放置商品详情内容

### 5. 底部固定按钮
- **背景色**: #4D40E5
- **文字**: "立即结算"
- **样式**: 圆角按钮，固定在底部
- **安全区域适配**: 支持iPhone底部安全区域

## 技术实现

### 组件依赖
```javascript
import CustomNavbar from '@/components/custom-navbar/custom-navbar.vue'
```

### 页面配置
```json
{
  "path": "subpages/product/detail",
  "style": {
    "navigationStyle": "custom"
  }
}
```

### 主要功能

#### 轮播图切换
```javascript
onSwiperChange(e) {
  this.currentImageIndex = e.detail.current;
}
```

#### 商品详情加载
```javascript
async loadProductDetail() {
  // 调用API获取商品详情
  // const result = await getProductDetail(this.productInfo.id);
}
```

#### 立即结算
```javascript
handleCheckout() {
  // 处理结算逻辑
}
```

## 样式特点

### 1. 响应式设计
- 适配不同屏幕尺寸
- 支持安全区域

### 2. 视觉效果
- 圆角设计
- 阴影效果
- 渐变背景

### 3. 交互反馈
- 按钮点击效果
- 轮播图滑动效果

## 使用方法

### 1. 页面跳转
```javascript
uni.navigateTo({
  url: '/subpages/product/detail?id=123'
});
```

### 2. 参数传递
- `id`: 商品ID，用于获取商品详情

## API集成

页面预留了API接口调用位置：

```javascript
// 在 api/product.js 中已有相关接口
import { getProductDetail } from '@/api/product.js'

// 在页面中调用
const result = await getProductDetail(productId);
```

## 测试页面

创建了测试页面 `pages/product-test/product-test.vue` 用于快速访问商品详情页。

## 注意事项

1. **页面配置**: 需要在 `pages.json` 中添加商品分包配置
2. **图片资源**: 当前使用项目中现有图片作为示例
3. **API接口**: 需要根据实际后端接口调整数据结构
4. **样式适配**: 已适配微信小程序环境

## 后续扩展

1. 添加商品规格选择
2. 添加收藏功能
3. 添加分享功能
4. 添加商品评价展示
5. 添加相关商品推荐
