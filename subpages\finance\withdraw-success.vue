<template>
	<view class="page">

		<!-- 成功图标和内容 -->
		<view class="success-content">
			<view class="success-icon">
				<image class="success-img" src="/static/images/img_succeed_default.png" mode="aspectFit"></image>
			</view>
			
			<view class="success-text">
				<view class="main-title">提现申请成功</view>
				<view class="sub-title">提现¥{{ withdrawAmount }}至微信申请成功，正在等待处理，预计7个工作日到账</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			withdrawAmount: '10.00' // 提现金额，从上一页传递过来
		}
	},
	onLoad(options) {
		// 接收从提现页面传递过来的参数
		if (options.amount) {
			this.withdrawAmount = options.amount;
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style scoped>
.page {
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

/* 头部 */
.header {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 30rpx;
	background-color: #ffffff;
}

.back-btn {
	position: absolute;
	left: 30rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
}

/* 成功内容区域 */
.success-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 0 60rpx;
	margin-top: -100rpx; /* 向上偏移一些，让内容更居中 */
}

.success-icon {
	margin-bottom: 60rpx;
}

.success-img {
	width: 200rpx;
	height: 200rpx;
}

.success-text {
	text-align: center;
}

.main-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 30rpx;
}

.sub-title {
	font-size: 28rpx;
	color: #999999;
	line-height: 1.6;
	padding: 0 20rpx;
}
</style>
