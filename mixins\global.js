/**
 * 全局混入
 */

import { userStorage } from '@/utils/storage.js';
import { formatMoney, formatDate, showToast, showConfirm, navigateTo } from '@/utils/common.js';

export default {
	data() {
		return {
			// 全局加载状态
			globalLoading: false,
			// 用户信息
			userInfo: null,
			// 是否已登录
			isLoggedIn: false
		};
	},
	
	computed: {
		// 获取用户头像
		userAvatar() {
			return this.userInfo?.avatar || '/static/images/default-avatar.png';
		},
		
		// 获取用户昵称
		userName() {
			return this.userInfo?.nickname || this.userInfo?.phone || '未登录';
		},
		
		// 是否为VIP用户
		isVip() {
			return this.userInfo?.vip || false;
		}
	},
	
	onLoad() {
		// 页面加载时检查登录状态
		this.checkLoginStatus();
	},
	
	onShow() {
		// 页面显示时刷新用户信息
		this.refreshUserInfo();
	},
	
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const token = userStorage.getToken();
			const userInfo = userStorage.getUserInfo();
			
			this.isLoggedIn = !!token;
			this.userInfo = userInfo;
			
			return this.isLoggedIn;
		},
		
		// 刷新用户信息
		refreshUserInfo() {
			if (this.isLoggedIn) {
				this.userInfo = userStorage.getUserInfo();
			}
		},
		
		// 登录检查装饰器
		requireLogin(callback) {
			if (!this.checkLoginStatus()) {
				this.showLoginModal();
				return false;
			}
			
			if (typeof callback === 'function') {
				callback();
			}
			return true;
		},
		
		// 显示登录提示
		showLoginModal() {
			uni.showModal({
				title: '提示',
				content: '请先登录',
				confirmText: '去登录',
				success: (res) => {
					if (res.confirm) {
						this.goToLogin();
					}
				}
			});
		},
		
		// 跳转到登录页
		goToLogin() {
			navigateTo('/pages/login/login', {}, 'navigate');
		},
		
		// 退出登录
		logout() {
			showConfirm('确定要退出登录吗？').then((confirm) => {
				if (confirm) {
					userStorage.clearUserData();
					this.isLoggedIn = false;
					this.userInfo = null;
					
					showToast('已退出登录');
					
					// 跳转到首页
					navigateTo('/pages/index/index', {}, 'reLaunch');
				}
			});
		},
		
		// 格式化金额
		$formatMoney(amount, decimals = 2) {
			return formatMoney(amount, decimals);
		},
		
		// 格式化日期
		$formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
			return formatDate(date, format);
		},
		
		// 显示提示
		$showToast(title, icon = 'none', duration = 2000) {
			return showToast(title, icon, duration);
		},
		
		// 显示确认对话框
		$showConfirm(content, title = '提示') {
			return showConfirm(content, title);
		},
		
		// 页面跳转
		$navigateTo(url, params = {}, type = 'navigate') {
			return navigateTo(url, params, type);
		},
		
		// 复制到剪贴板
		$copyText(text, successMsg = '复制成功') {
			uni.setClipboardData({
				data: text,
				success: () => {
					this.$showToast(successMsg, 'success');
				},
				fail: () => {
					this.$showToast('复制失败', 'none');
				}
			});
		},
		
		// 拨打电话
		$makePhoneCall(phoneNumber) {
			uni.makePhoneCall({
				phoneNumber,
				fail: () => {
					this.$showToast('拨号失败', 'none');
				}
			});
		},
		
		// 预览图片
		$previewImage(urls, current = 0) {
			if (typeof urls === 'string') {
				urls = [urls];
			}
			
			uni.previewImage({
				urls,
				current: typeof current === 'number' ? current : urls.indexOf(current)
			});
		},
		
		// 选择图片
		$chooseImage(options = {}) {
			return new Promise((resolve, reject) => {
				uni.chooseImage({
					count: options.count || 1,
					sizeType: options.sizeType || ['compressed'],
					sourceType: options.sourceType || ['album', 'camera'],
					success: resolve,
					fail: reject
				});
			});
		},
		
		// 获取位置信息
		$getLocation(options = {}) {
			return new Promise((resolve, reject) => {
				uni.getLocation({
					type: options.type || 'gcj02',
					success: resolve,
					fail: reject
				});
			});
		},
		
		// 选择位置
		$chooseLocation() {
			return new Promise((resolve, reject) => {
				uni.chooseLocation({
					success: resolve,
					fail: reject
				});
			});
		},
		
		// 扫码
		$scanCode(options = {}) {
			return new Promise((resolve, reject) => {
				uni.scanCode({
					scanType: options.scanType || ['qrCode', 'barCode'],
					success: resolve,
					fail: reject
				});
			});
		},
		
		// 分享
		$share(options = {}) {
			return new Promise((resolve, reject) => {
				uni.share({
					provider: options.provider || 'weixin',
					scene: options.scene || 'WXSceneSession',
					type: options.type || 0,
					href: options.href || '',
					title: options.title || '',
					summary: options.summary || '',
					imageUrl: options.imageUrl || '',
					success: resolve,
					fail: reject
				});
			});
		},
		
		// 设置页面标题
		$setNavigationBarTitle(title) {
			uni.setNavigationBarTitle({
				title
			});
		},
		
		// 显示/隐藏导航栏加载动画
		$showNavigationBarLoading() {
			uni.showNavigationBarLoading();
		},
		
		$hideNavigationBarLoading() {
			uni.hideNavigationBarLoading();
		},
		
		// 下拉刷新
		$startPullDownRefresh() {
			uni.startPullDownRefresh();
		},
		
		$stopPullDownRefresh() {
			uni.stopPullDownRefresh();
		},
		
		// 页面滚动到顶部
		$pageScrollTo(scrollTop = 0, duration = 300) {
			uni.pageScrollTo({
				scrollTop,
				duration
			});
		},
		
		// 获取系统信息
		$getSystemInfo() {
			return new Promise((resolve, reject) => {
				uni.getSystemInfo({
					success: resolve,
					fail: reject
				});
			});
		},
		
		// 获取网络状态
		$getNetworkType() {
			return new Promise((resolve, reject) => {
				uni.getNetworkType({
					success: resolve,
					fail: reject
				});
			});
		},
		
		// 监听网络状态变化
		$onNetworkStatusChange(callback) {
			uni.onNetworkStatusChange(callback);
		},
		
		// 振动
		$vibrate(type = 'short') {
			if (type === 'long') {
				uni.vibrateLong();
			} else {
				uni.vibrateShort();
			}
		}
	}
};
