<template>
	<view class="forgot-password-container">
		
		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 找回密码标题 -->
			<view class="page-title">找回密码</view>
			
			<!-- 输入框组合容器 -->
			<view class="input-group-container">
				<!-- 图片验证码输入框 -->
				<view class="input-item">
					<input
						class="img-code-input"
						type="text"
						placeholder="请输入图片验证码"
						v-model="imgCode"
						maxlength="6"
					/>
					<image
						class="captcha-image"
						:src="captchaUrl"
						mode="aspectFit"
						@click="refreshCaptcha"
					/>
				</view>

				<!-- 分割线 -->
				<view class="divider-line"></view>

				<!-- 手机号输入框 -->
				<view class="input-item">
					<input
						class="phone-input"
						type="number"
						placeholder="请输入手机号"
						v-model="phoneNumber"
						maxlength="11"
					/>
					<text class="verification-code" @click="sendCode" :class="countdown > 0 ? 'disabled' : ''">
						{{ getCodeButtonText() }}
					</text>
				</view>

				<!-- 分割线 -->
				<view class="divider-line"></view>

				<!-- 短信验证码输入框 -->
				<view class="input-item">
					<input
						class="code-input"
						type="text"
						placeholder="请输入短信验证码"
						v-model="verificationCode"
						maxlength="6"
					/>
				</view>

				<!-- 分割线 -->
				<view class="divider-line"></view>

				<!-- 新密码输入框 -->
				<view class="input-item">
					<input
						class="password-input"
						type="password"
						placeholder="请输入新密码"
						v-model="newPassword"
						maxlength="20"
					/>
				</view>
			</view>
			
			<!-- 提交按钮 -->
			<view class="submit-btn-container">
				<button class="submit-btn" @click="handleSubmit">提交</button>
			</view>
		</view>
	</view>
</template>

<script>
import { userApi } from '@/api/index.js';

export default {
	data() {
		return {
			statusBarHeight: 0,
			phoneNumber: '',
			verificationCode: '',
			imgCode: '',
			newPassword: '',
			countdown: 0,
			timer: null,
			codeSent: false,
			deviceID: '',
			captchaUrl: ''
		}
	},
	
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		// 初始化验证码图片
		this.refreshCaptcha();
	},
	
	onUnload() {
		// 清除定时器
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 生成随机字符串
		generateRandomString(length) {
			const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
			let result = '';
			for (let i = 0; i < length; i++) {
				const randomIndex = Math.floor(Math.random() * characters.length);
				result += characters.charAt(randomIndex);
			}
			return result;
		},

		// 刷新验证码图片
		refreshCaptcha() {
			this.deviceID = this.generateRandomString(6);
			this.captchaUrl = 'http://ooseek.iepose.cn/app/auth/genCaptcha?DeviceID=' + this.deviceID;
		},
		
		// 获取验证码按钮文本
		getCodeButtonText() {
			if (this.countdown > 0) {
				return this.countdown + 's';
			}
			if (!this.phoneNumber) {
				return '发送验证码';
			}
			return this.codeSent ? '重新发送' : '发送验证码';
		},

		// 发送验证码
		async sendCode() {
			// 如果正在倒计时，不允许重复发送
			if (this.countdown > 0) {
				return;
			}

			// 检查图片验证码是否为空
			if (!this.imgCode) {
				uni.showToast({
					title: '请输入图片验证码',
					icon: 'none'
				});
				return;
			}

			// 检查手机号是否为空
			if (!this.phoneNumber) {
				uni.showToast({
					title: '请输入手机号',
					icon: 'none'
				});
				return;
			}

			// 验证手机号格式
			if (!/^1[3-9]\d{9}$/.test(this.phoneNumber)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}

			try {
				// 显示加载提示
				uni.showLoading({
					title: '发送中...'
				});

				// 调试信息：打印参数
				console.log('发送验证码参数:', {
					phone: this.phoneNumber,
					deviceID: this.deviceID,
					imgCode: this.imgCode
				});

				// 调用发送验证码API
				const res = await userApi.sendSmsCode(this.phoneNumber, this.deviceID, this.imgCode);
				console.log('发送验证码响应:', res);

				uni.hideLoading();

				// 标记已发送验证码
				this.codeSent = true;

				// 开始倒计时60秒
				this.countdown = 60;
				this.timer = setInterval(() => {
					this.countdown--;
					if (this.countdown <= 0) {
						clearInterval(this.timer);
						this.timer = null;
					}
				}, 1000);

				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				});

			} catch (error) {
				uni.hideLoading();
				console.error('发送验证码失败:', error);

				// 刷新验证码图片
				this.refreshCaptcha();
				this.imgCode = '';

				uni.showToast({
					title: error.msg || '发送失败，请重试',
					icon: 'none'
				});
			}
		},
		
		// 提交重置密码
		async handleSubmit() {
			// 验证图片验证码
			if (!this.imgCode) {
				uni.showToast({
					title: '请输入图片验证码',
					icon: 'none'
				});
				return;
			}

			// 验证手机号
			if (!this.phoneNumber) {
				uni.showToast({
					title: '请输入手机号',
					icon: 'none'
				});
				return;
			}

			if (!/^1[3-9]\d{9}$/.test(this.phoneNumber)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}

			// 验证短信验证码
			if (!this.verificationCode) {
				uni.showToast({
					title: '请输入短信验证码',
					icon: 'none'
				});
				return;
			}

			// 验证新密码
			if (!this.newPassword) {
				uni.showToast({
					title: '请输入新密码',
					icon: 'none'
				});
				return;
			}

			if (this.newPassword.length < 6) {
				uni.showToast({
					title: '密码长度不能少于6位',
					icon: 'none'
				});
				return;
			}

			try {
				// 显示加载提示
				uni.showLoading({
					title: '提交中...'
				});

				// 调用重置密码API
				const res = await userApi.resetPassword({
					code: this.verificationCode,
					passwd: this.newPassword,
					phone: this.phoneNumber,
					tenantId: 3
				});

				console.log('重置密码响应:', res);
				uni.hideLoading();

				uni.showToast({
					title: '密码重置成功',
					icon: 'success'
				});

				// 延迟跳转到登录页面
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}, 1500);

			} catch (error) {
				uni.hideLoading();
				console.error('重置密码失败:', error);

				uni.showToast({
					title: error.msg || '重置失败，请重试',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style scoped>
.forgot-password-container {
	width: 100%;
	min-height: 100vh;
	background-color: #F6F8FF;
}

.status-bar {
	width: 100%;
	background-color: #F6F8FF;
}

/* 导航栏 */
.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 30rpx;
	background-color: #F6F8FF;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	font-size: 48rpx;
	color: #333333;
	font-weight: bold;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333333;
}

.nav-right {
	width: 60rpx;
}

/* 主要内容 */
.main-content {
	padding: 60rpx 30rpx;
}

.page-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #000000;
	margin-bottom: 60rpx;
}

/* 输入框组合容器 */
.input-group-container {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	overflow: hidden;
	margin-bottom: 40rpx;
}

.input-item {
	position: relative;
	height: 120rpx;
	display: flex;
	align-items: center;
}

.phone-input,
.code-input,
.img-code-input,
.password-input {
	flex: 1;
	height: 100%;
	padding: 0 30rpx;
	font-size: 32rpx;
	color: #333333;
	border: none;
	background-color: transparent;
}

.phone-input::placeholder,
.code-input::placeholder,
.img-code-input::placeholder,
.password-input::placeholder {
	color: #CCCCCC;
}

/* 分割线 */
.divider-line {
	height: 1rpx;
	background-color: #F0F0F0;
	margin: 0 30rpx;
}

/* 验证码图片 */
.captcha-image {
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 150rpx;
	height: 80rpx;
	border: 1rpx solid #E5E5E5;
	border-radius: 8rpx;
	z-index: 10;
}

/* 验证码按钮 */
.verification-code {
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 28rpx;
	color: #6366F1;
	white-space: nowrap;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	z-index: 10;
}

.verification-code.disabled {
	color: #CCCCCC !important;
	pointer-events: none;
}

/* 提交按钮 */
.submit-btn-container {
	margin-top: 120rpx;
}

.submit-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
	border-radius: 20rpx;
	border: none;
	color: #FFFFFF;
	font-size: 36rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn:active {
	opacity: 0.8;
}
</style>
