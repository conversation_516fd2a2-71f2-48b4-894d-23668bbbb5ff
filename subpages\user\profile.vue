<template>
	<view class="profile-page">
		<!-- 背景图片 -->
		<image class="background-image" src="/static/images/drawable-hdpi_bg_home.png" mode="aspectFill"></image>

		<!-- 自定义导航栏 -->
		<custom-navbar title="个人资料" @back="goBack"></custom-navbar>

		<!-- 内容区域 -->
		<view class="content-area">
			<!-- 顶部内容区域 -->
			<view class="top-content-section">
				<!-- 完善个人资料标题 -->
				<view class="profile-header">
					<view class="profile-title">完善个人资料</view>
					<view class="profile-subtitle">可编辑更改您的个人资料</view>
				</view>

				<!-- 头像区域 -->
				<view class="avatar-section">
					<view class="avatar-container" @click="changeAvatar">
						<image class="avatar-image" :src="userAvatar" mode="aspectFill"></image>
						<view class="camera-overlay">
							<image class="camera-icon" src="/static/images/drawable-hdpi_icon_head_projection.png" mode="aspectFill"></image>
							<text class="camera-text">更换头像</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 表单区域 -->
			<view class="form-section">
			<!-- 姓名显示框 -->
			<view class="form-item">
				<view class="form-label">姓名</view>
				<view class="form-input-container">
					<view class="form-display">{{ formData.userName || '暂无' }}</view>
				</view>
			</view>

			<!-- 手机号显示框 -->
			<view class="form-item">
				<view class="form-label">手机号</view>
				<view class="form-input-container">
					<view class="form-display">{{ formData.phone || '暂无' }}</view>
				</view>
			</view>
		</view>


		</view>
	</view>
</template>

<script>
	import CustomNavbar from '@/components/custom-navbar/custom-navbar.vue'
	import { userApi } from '@/api/index.js';

	export default {
		components: {
			CustomNavbar
		},
		data() {
			return {
				formData: {
					userName: '',
					phone: '',
					avatar: '',
					userType: '',
					userTypeText: ''
				},
				originalData: {
					userName: '',
					phone: '',
					avatar: '',
					userType: '',
					userTypeText: ''
				}
			}
		},

		computed: {
			// 用户头像
			userAvatar() {
				return this.formData.avatar || '/static/images/default-avatar.png';
			}
		},
		
		onLoad() {
			this.getUserInfo();
		},
		
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 获取用户信息
			async getUserInfo() {
				try {
					const res = await userApi.getUserInfo();
					console.log('用户信息:', res);

					// 处理用户数据
					if (res.data && res.data.user) {
						const user = res.data.user;
						const userData = {
							userName: user.userName || '暂无',
							phone: user.phone || '暂无',
							avatar: user.imgUrl || '/static/images/default-avatar.png',
							userType: user.linkType || '',
							userTypeText: this.getUserTypeText(user.linkType)
						};

						this.formData = { ...userData };
						this.originalData = { ...userData };
					}
				} catch (error) {
					console.error('获取用户信息失败:', error);
				}
			},

			// 根据 linkType 获取用户类型文本
			getUserTypeText(linkType) {
				switch (linkType) {
					case '0':
						return '代理商';
					case '1':
						return '团队';
					case '2':
						return '员工';
					default:
						return '';
				}
			},
			

			
			// 更换头像
			changeAvatar() {
				uni.showActionSheet({
					itemList: ['拍照', '从相册选择'],
					success: (res) => {
						const sourceType = res.tapIndex === 0 ? ['camera'] : ['album'];
						this.chooseImage(sourceType);
					}
				});
			},
			
			// 选择图片
			chooseImage(sourceType) {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: sourceType,
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						// 先显示本地图片
						this.formData.avatar = tempFilePath;
						// 上传图片到服务器
						this.uploadAvatar(tempFilePath);
					},
					fail: () => {
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},

			// 上传头像
			async uploadAvatar(filePath) {
				try {
					uni.showLoading({
						title: '上传中...'
					});

					// 调用上传文件接口
					const uploadRes = await userApi.uploadFile(filePath);
					console.log('上传文件响应:', uploadRes);

					if (uploadRes.data && uploadRes.data.url) {
						// 调用修改头像接口
						const updateRes = await userApi.updateUserPhoto(uploadRes.data.url);
						console.log('修改头像响应:', updateRes);

						// 更新本地头像数据
						this.formData.avatar = uploadRes.data.url;
						this.originalData.avatar = uploadRes.data.url;

						uni.hideLoading();
						uni.showToast({
							title: '头像更新成功',
							icon: 'success'
						});
					} else {
						throw new Error('上传失败，未获取到图片地址');
					}
				} catch (error) {
					uni.hideLoading();
					console.error('上传头像失败:', error);

					// 恢复原来的头像
					this.formData.avatar = this.originalData.avatar;

					uni.showToast({
						title: error.msg || '头像更新失败',
						icon: 'none'
					});
				}
			},
			

		}
	}
</script>

<style scoped>
	.profile-page {
		min-height: 100vh;
		position: relative;
	}

	/* 背景图片样式 */
	.background-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 490rpx;
	}





	/* 内容区域 */
	.content-area {
		width: 100%;
		box-sizing: border-box;
		padding: 0 30rpx;
		background-color: #ffffff;
		min-height: 100vh;
		padding-top: 170rpx; /* 为背景图片和导航栏留出空间 */
	}

	/* 顶部内容区域 */
	.top-content-section {
		padding: 30rpx 0 40rpx;
		position: relative;
	}

	.profile-header {
		position: relative;
		z-index: 2;
		margin-bottom: 60rpx;
		padding: 0 0 20rpx 0;
		text-align: left;
	}

	.profile-title {
		font-size: 40rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 16rpx;
	}

	.profile-subtitle {
		font-size: 28rpx;
		color: #666666;
	}

	/* 头像区域 */
	.avatar-section {
		position: relative;
		z-index: 2;
		display: flex;
		justify-content: center;
	}

	.avatar-container {
		position: relative;
		width: 240rpx;
		height: 240rpx;
	}

	.avatar-image {
		width: 240rpx;
		height: 240rpx;
		border-radius: 120rpx;
	}

	.camera-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 240rpx;
		height: 240rpx;
		border-radius: 120rpx;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 12rpx;
	}

	.camera-icon {
		width: 48rpx;
		height: 48rpx;
	}

	.camera-text {
		font-size: 22rpx;
		color: #ffffff;
	}

	/* 表单区域 */
	.form-section {
		padding: 40rpx;
		position: relative;
		z-index: 3;
	}

	.form-item {
		background: #f5f5f5;
		border-radius: 24rpx;
		margin-bottom: 24rpx;
		padding: 12rpx 40rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.form-label {
		font-size: 32rpx;
		color: #333333;
		font-weight: 400;
		min-width: 120rpx;
	}

	.form-input-container {
		flex: 1;
		margin-left: 40rpx;
	}

	.form-input {
		width: 100%;
		height: 80rpx;
		background: transparent;
		border: none;
		padding: 0;
		font-size: 32rpx;
		color: #333333;
		text-align: right;
		box-sizing: border-box;
	}

	.form-input:focus {
		outline: none;
	}

	.form-input::placeholder {
		color: #999999;
	}

	/* 显示文本样式 */
	.form-display {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		font-size: 32rpx;
		color: #333333;
		text-align: right;
		box-sizing: border-box;
	}


</style>
