<template>
	<view class="address-list-page">
		<!-- 地址列表 -->
		<view class="address-list">
			<view 
				v-for="(item, index) in addressList" 
				:key="index" 
				class="address-item-wrapper"
			>
				<view
					class="address-item"
					:style="{ transform: `translateX(${item.translateX || 0}rpx)` }"
					@touchstart="touchStart($event, index)"
					@touchmove="touchMove($event, index)"
					@touchend="touchEnd($event, index)"
				>
					<!-- 地址信息 -->
					<view class="address-info" @click="selectAddress(item)">
						<view class="user-info">
							<text class="user-name">{{ item.name }}</text>
							<text class="user-phone">{{ item.phone }}</text>
							<view v-if="item.isDefault" class="default-tag">默认</view>
						</view>
						<view class="address-detail">
							{{ item.detail }}
						</view>
					</view>
					
					<!-- 编辑按钮 -->
					<view class="edit-btn" @click.stop="editAddress(item)">
						<image class="edit-icon" src="/static/images/icon_edit.png" mode="aspectFit"></image>
					</view>
				</view>
				
				<!-- 删除按钮 -->
				<view v-if="!item.isDefault" class="delete-btn" @click="deleteAddress(index)">
					<text class="delete-text">删除</text>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view v-if="addressList.length === 0" class="empty-state">
			<image class="empty-icon" src="/static/images/noAddress.png" mode="aspectFit"></image>
			<text class="empty-text">暂无收货地址</text>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-btn">
			<view class="add-address-btn" @click="addAddress">
				新建收货地址
			</view>
		</view>
	</view>
</template>

<script>
import { addressApi } from '@/api/index.js'

export default {
	data() {
		return {
			addressList: [],
			touchData: {
				startX: 0,
				startY: 0,
				isMoving: false,
				deleteWidth: 160 // rpx
			},
			isSelectMode: false, // 是否为选择地址模式
			fromPage: '' // 来源页面
		}
	},
	
	onLoad(options) {
		// 检查是否从订单页面跳转过来
		if (options.from === 'order') {
			this.isSelectMode = true;
			this.fromPage = 'order';
		}
		this.loadAddressList();
	},

	onShow() {
		this.loadAddressList();
	},
	
	methods: {
		// 加载地址列表
		async loadAddressList() {
			try {
				const res = await addressApi.getAddressList();
				if (res.code === 0) {
					this.addressList = res.data.map(item => ({
						id: item.id,
						name: item.realName,
						phone: item.contactPhone,
						province: '', // 省市区信息已经包含在detailAddress中
						city: '',
						district: '',
						detail: item.detailAddress, // 完整地址
						isDefault: item.isDefault === "1", // 转换为布尔值
						translateX: 0, // 添加滑动状态
						// 保留原始数据用于编辑
						originalData: item
					}));
				}
			} catch (error) {
				console.error('获取地址列表失败:', error);
				uni.showToast({
					title: '获取地址列表失败',
					icon: 'none'
				});
			}
		},
		
		// 触摸开始
		touchStart(e, index) {
			this.touchData.isMoving = false;
			let item = this.addressList[index];
			if (item.isDefault) return;
			
			let touch = e.touches[0];
			this.touchData.startX = touch.clientX;
			this.touchData.startY = touch.clientY;
			this.resetAllSwipeState(index);
		},
		
		// 触摸移动
		touchMove(e, index) {
			let item = this.addressList[index];
			if (item.isDefault) return;
		
			this.touchData.isMoving = true;
		
			let touch = e.touches[0];
			let deltaX = this.touchData.startX - touch.clientX;
			let deltaY = this.touchData.startY - touch.clientY;
		
			if (Math.abs(deltaY) > Math.abs(deltaX)) {
				return;
			}
		
			if (deltaX > 0) {
				let translateX = -Math.min(deltaX * 2, this.touchData.deleteWidth);
				this.$set(item, 'translateX', translateX);
			} else {
				this.$set(item, 'translateX', 0);
			}
		},
		
		// 触摸结束
		touchEnd(e, index) {
			let item = this.addressList[index];
			if (item.isDefault || !this.touchData.isMoving) {
				return;
			}
			
			this.touchData.isMoving = false;
		
			let translateX = item.translateX || 0;
			let deleteWidth = this.touchData.deleteWidth;
		
			if (Math.abs(translateX) > deleteWidth / 2) {
				this.$set(item, 'translateX', -deleteWidth);
			} else {
				this.$set(item, 'translateX', 0);
			}
		},

		// 重置所有滑动状态
		resetAllSwipeState(currentIndex) {
			this.addressList.forEach((item, index) => {
				if (index !== currentIndex && item.translateX !== 0) {
					this.$set(item, 'translateX', 0);
				}
			});
		},
		
		// 选择地址
		selectAddress(item) {
			if (this.touchData.isMoving) return;
			
			if (this.isSelectMode) {
				// 选择地址模式，返回选中的地址
				uni.setStorageSync('selectedAddress', item);
				uni.navigateBack();
			}
		},
		
		// 选择地址
		selectAddress(item) {
			if (this.isSelectMode && this.fromPage === 'order') {
				// 返回订单确认页面并传递选中的地址
				const pages = getCurrentPages();
				const prevPage = pages[pages.length - 2]; // 上一个页面
				if (prevPage) {
					prevPage.$vm.selectedAddress = item;
				}
				uni.navigateBack();
			} else {
				// 普通模式，跳转到编辑页面
				this.editAddress(item);
			}
		},

		// 编辑地址
		editAddress(item) {
			uni.navigateTo({
				url: `/subpages/address/edit?id=${item.id}`
			});
		},
		
		// 删除地址
		async deleteAddress(index) {
			this.resetAllSwipeState();
			let item = this.addressList[index];

			uni.showModal({
				title: '提示',
				content: '确定删除该地址吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({
								title: '删除中...',
								mask: true
							});

							const result = await addressApi.deleteAddress(item.id);

							uni.hideLoading();

							if (result.code === 0) {
								this.addressList.splice(index, 1);
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
							}
						} catch (error) {
							uni.hideLoading();
							console.error('删除地址失败:', error);
							uni.showToast({
								title: '删除失败，请重试',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 新增地址
		addAddress() {
			uni.navigateTo({
				url: '/subpages/address/edit'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.address-list-page {
	min-height: 100vh;
	background-color: #F5F5F5;
	padding: 20rpx 30rpx 140rpx;
}

.address-list {
	.address-item-wrapper {
		position: relative;
		overflow: hidden;
		border-radius: 14rpx;
		margin-bottom: 20rpx;
		
		.address-item {
			position: relative;
			z-index: 2;
			background-color: #fff;
			transition: transform 0.3s ease;
			display: flex;
			align-items: center;
			padding: 30rpx 20rpx;
			
			.address-info {
				flex: 1;
				
				.user-info {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;
					
					.user-name {
						font-size: 32rpx;
						font-weight: 600;
						color: #222222;
						margin-right: 20rpx;
					}
					
					.user-phone {
						font-size: 28rpx;
						color: #222222;
						margin-right: 20rpx;
					}
					
					.default-tag {
						background: #4D40E5;
						color: #ffffff;
						font-size: 20rpx;
						padding: 6rpx 12rpx;
						border-radius: 6rpx;
					}
				}
				
				.address-detail {
					font-size: 26rpx;
					color: #666666;
					line-height: 1.4;
				}
			}
			
			.edit-btn {
				padding: 10rpx;
				margin-left: 20rpx;
				border-left: 1px solid #EFEFEF;
				padding-left: 20rpx;
				
				.edit-icon {
					width: 36rpx;
					height: 36rpx;
				}
			}
		}
		
		.delete-btn {
			position: absolute;
			top: 0;
			right: 0;
			width: 160rpx;
			height: 100%;
			background-color: #FF5400;
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 1;
			
			.delete-text {
				color: #ffffff;
				font-size: 28rpx;
				font-weight: 500;
			}
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
	
	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
}

.bottom-btn {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 20rpx 30rpx;
	background-color: #F5F5F5;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	
	.add-address-btn {
		width: 100%;
		height: 88rpx;
		background: #4D40E5;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
	}
}
</style>
