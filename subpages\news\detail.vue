<template>
	<view class="page">
		<!-- 标题区域 -->
		<view class="header" v-if="records.title">
			<text class="title">{{ records.title }}</text>
			<text class="date" v-if="records.createDate">{{ formatTime(records.createDate) }}</text>
		</view>

		<!-- 介绍区域 -->
		<view class="intro-section" v-if="records.intro">
			<text class="intro-text">{{ records.intro }}</text>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<image v-if="flag" :src="records.picEditor" mode="widthFix" class="content-image"></image>
			<video v-else id="myVideo" :src="records.picEditor" class="content-video"></video>
		</view>
	</view>
</template>

<script>
	import { businessSchoolApi } from '@/api/index.js';

	export default {
		data() {
			return {
				id: 0,
				flag: true,
				records:{},
				goods: [],
			}
		},
		onLoad(o) {
			console.log(o);
			this.id = o.id
			this.getpiclis();
		},
		onShow() {
		},
		methods: {
			async getpiclis() {
				try {
					uni.showLoading({
						title: '加载中...',
						mask: true
					});

					// 调用商学院图片信息接口
					const result = await businessSchoolApi.getPicList({
						page: 1,
						limit: 100 // 获取足够多的数据以便根据ID查找
					});

					uni.hideLoading();

					console.log('商学院图片详情响应数据:', result);

					if (result.code === 0 || result.code === 200) {
						this.goods = result.data || [];

						// 根据ID查找对应的记录
						const targetRecord = this.goods.find(item => (item.id-1) == Number(this.id));

						if (targetRecord) {
							this.records = targetRecord;
							// 判断是否为图片格式
							this.flag = this.isImageFormat(targetRecord.picEditor);
						} else {
							uni.showToast({
								title: '未找到对应的内容',
								icon: 'none'
							});
						}
					} else {
						uni.showToast({
							title: result.msg || '加载失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('加载商学院图片详情失败:', error);
					uni.showToast({
						title: '加载失败，请重试',
						icon: 'none'
					});
				}
			},

			// 判断是否为图片格式
			isImageFormat(url) {
				if (!url) return true;
				const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'webp'];
				const extension = url.split('.').pop().toLowerCase();
				return imageExtensions.includes(extension);
			},

			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #f8f8f8;
		min-height: 100vh;
	}

	.header {
		background-color: white;
		padding: 32rpx 24rpx;
		margin-bottom: 20rpx;
	}

	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333333;
		line-height: 50rpx;
		display: block;
		margin-bottom: 16rpx;
	}

	.date {
		font-size: 24rpx;
		color: #999999;
		line-height: 32rpx;
	}

	.intro-section {
		background-color: white;
		padding: 24rpx;
		margin-bottom: 20rpx;
	}

	.intro-text {
		font-size: 28rpx;
		color: #666666;
		line-height: 40rpx;
	}

	.content {
		background-color: white;
		padding: 24rpx;
	}

	.content-image {
		width: 100%;
		border-radius: 12rpx;
	}

	.content-video {
		width: 100%;
		height: 400rpx;
		border-radius: 12rpx;
	}
</style>